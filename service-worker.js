// Service Worker for <PERSON>'s Website
// Enables offline functionality and caching

const CACHE_NAME = 'mohamed-alashrafi-v1.0.0';
const urlsToCache = [
    './',
    './index.html',
    './styles/main.css',
    './js/main.js',
    './js/portfolio.js',
    './js/ai-assistant.js',
    './assets/logo.png',
    './assets/profile.jpg',
    './assets/favicon.png',
    './manifest.json',
    // Add more static assets as needed
];

// Install event - cache resources
self.addEventListener('install', function(event) {
    event.waitUntil(
        caches.open(CACHE_NAME)
            .then(function(cache) {
                console.log('Opened cache');
                return cache.addAll(urlsToCache);
            })
    );
});

// Fetch event - serve cached content when offline
self.addEventListener('fetch', function(event) {
    event.respondWith(
        caches.match(event.request)
            .then(function(response) {
                // Cache hit - return response
                if (response) {
                    return response;
                }
                return fetch(event.request);
            }
        )
    );
});

// Activate event - clean up old caches
self.addEventListener('activate', function(event) {
    event.waitUntil(
        caches.keys().then(function(cacheNames) {
            return Promise.all(
                cacheNames.map(function(cacheName) {
                    if (cacheName !== CACHE_NAME) {
                        return caches.delete(cacheName);
                    }
                })
            );
        })
    );
});

// Background sync for offline form submissions
self.addEventListener('sync', function(event) {
    if (event.tag === 'contact-form-sync') {
        event.waitUntil(syncContactForm());
    }
});

// Push notifications
self.addEventListener('push', function(event) {
    if (event.data) {
        const data = event.data.json();
        const options = {
            body: data.body,
            icon: '/assets/favicon.png',
            badge: '/assets/badge.png',
            vibrate: [100, 50, 100],
            data: {
                dateOfArrival: Date.now(),
                primaryKey: 1
            }
        };

        event.waitUntil(
            self.registration.showNotification(data.title, options)
        );
    }
});

// Notification click handler
self.addEventListener('notificationclick', function(event) {
    event.notification.close();
    
    event.waitUntil(
        clients.openWindow('/')
    );
});

// Sync contact form when online
async function syncContactForm() {
    // Get pending form submissions from IndexedDB
    const pendingSubmissions = await getPendingSubmissions();
    
    for (const submission of pendingSubmissions) {
        try {
            // Try to submit the form
            await submitContactForm(submission);
            // Remove from pending if successful
            await removePendingSubmission(submission.id);
        } catch (error) {
            console.error('Failed to sync form submission:', error);
        }
    }
}

// Helper functions for IndexedDB operations
async function getPendingSubmissions() {
    return new Promise((resolve, reject) => {
        const request = indexedDB.open('MohamedAlAshrafi', 1);
        
        request.onsuccess = function(event) {
            const db = event.target.result;
            const transaction = db.transaction(['pending_submissions'], 'readonly');
            const store = transaction.objectStore('pending_submissions');
            const getRequest = store.getAll();
            
            getRequest.onsuccess = function() {
                resolve(getRequest.result);
            };
            
            getRequest.onerror = function() {
                reject(getRequest.error);
            };
        };
        
        request.onerror = function() {
            reject(request.error);
        };
    });
}

async function removePendingSubmission(id) {
    return new Promise((resolve, reject) => {
        const request = indexedDB.open('MohamedAlAshrafi', 1);
        
        request.onsuccess = function(event) {
            const db = event.target.result;
            const transaction = db.transaction(['pending_submissions'], 'readwrite');
            const store = transaction.objectStore('pending_submissions');
            const deleteRequest = store.delete(id);
            
            deleteRequest.onsuccess = function() {
                resolve();
            };
            
            deleteRequest.onerror = function() {
                reject(deleteRequest.error);
            };
        };
        
        request.onerror = function() {
            reject(request.error);
        };
    });
}

async function submitContactForm(data) {
    // This would typically send to your backend
    // For now, we'll simulate the submission
    const formData = new FormData();
    Object.keys(data).forEach(key => {
        formData.append(key, data[key]);
    });
    
    const response = await fetch('/api/contact', {
        method: 'POST',
        body: formData
    });
    
    if (!response.ok) {
        throw new Error('Form submission failed');
    }
    
    return response.json();
}
/* Performance Optimizations */
html {
    scroll-behavior: smooth;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeLegibility;
}

/* Reduce layout shifts */
img, video {
    max-width: 100%;
    height: auto;
}

/* Optimize animations for performance */
@media (prefers-reduced-motion: reduce) {
    *, *::before, *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* GPU acceleration for smooth animations */
.hero-content,
.portfolio-item,
.certificate-card,
.review-card,
.service-card {
    will-change: transform;
    transform: translateZ(0);
}

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    /* v2.0 Enhanced Color System */
    --primary-color: #6366f1;
    --primary-dark: #4f46e5;
    --primary-light: #a5b4fc;
    --secondary-color: #f59e0b;
    --secondary-dark: #d97706;
    --secondary-light: #fbbf24;
    --accent-color: #ec4899;
    --accent-dark: #db2777;
    --accent-light: #f9a8d4;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --error-color: #ef4444;
    --error-color: #ef4444;
    --info-color: #3b82f6;

    /* Enhanced Dark Colors */
    --dark-color: #0f172a;
    --dark-secondary: #1e293b;
    --dark-tertiary: #334155;
    --light-color: #f8fafc;
    --light-secondary: #f1f5f9;
    --white-color: #ffffff;
    --gray-color: #64748b;
    --gray-light: #94a3b8;
    --gray-dark: #475569;

    /* v2.0 Modern Gradients */
    --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --gradient-secondary: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    --gradient-accent: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    --gradient-hero: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
    --gradient-card: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
    --gradient-dark: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
    --gradient-glass: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);

    /* Enhanced Shadows */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    --shadow-colored: 0 10px 25px -5px rgba(102, 126, 234, 0.25);
    --shadow-glow: 0 0 20px rgba(102, 126, 234, 0.3);

    /* Enhanced Transitions */
    --transition-fast: 0.15s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-normal: 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-slow: 0.5s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-bounce: 0.6s cubic-bezier(0.68, -0.55, 0.265, 1.55);

    /* Enhanced Border Radius */
    --border-radius: 12px;
    --border-radius-sm: 8px;
    --border-radius-lg: 16px;
    --border-radius-xl: 24px;
    --border-radius-2xl: 32px;
    --border-radius-full: 9999px;

    /* Spacing System */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-2xl: 3rem;
    --spacing-3xl: 4rem;
}

html {
    scroll-behavior: smooth;
    font-size: 16px;
}

body {
    font-family: 'Cairo', sans-serif;
    line-height: 1.6;
    color: var(--dark-color);
    background-color: var(--white-color);
    overflow-x: hidden;
    opacity: 1;
    visibility: visible;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Navigation */
.navbar {
    position: fixed;
    top: 0;
    width: 100%;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    z-index: 1000;
    transition: all var(--transition-normal);
}

.navbar.scrolled {
    background: rgba(255, 255, 255, 0.98);
    box-shadow: var(--shadow-md);
}

.nav-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 2rem;
    max-width: 1200px;
    margin: 0 auto;
}

.nav-logo {
    display: flex;
    align-items: center;
    gap: 10px;
}

.logo-m {
    width: 45px;
    height: 45px;
    background: var(--gradient-primary);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.8rem;
    font-weight: 900;
    color: var(--white-color);
    font-family: 'Cairo', sans-serif;
    text-transform: uppercase;
    letter-spacing: -1px;
    box-shadow: var(--shadow-md);
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.logo-m::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(45deg, rgba(255, 255, 255, 0.1) 0%, transparent 50%, rgba(255, 255, 255, 0.1) 100%);
    transform: translateX(-100%);
    transition: transform 0.6s ease;
}

.nav-logo:hover .logo-m::before {
    transform: translateX(100%);
}

.nav-logo:hover .logo-m {
    transform: translateY(-2px) scale(1.05);
    box-shadow: var(--shadow-lg);
}

.logo-text {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--primary-color);
}

.nav-menu {
    display: flex;
    gap: 2rem;
}

.nav-link {
    text-decoration: none;
    color: var(--dark-color);
    font-weight: 500;
    position: relative;
    transition: color var(--transition-fast);
}

.nav-link:hover,
.nav-link.active {
    color: var(--primary-color);
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--primary-color);
    transition: width var(--transition-normal);
}

.nav-link:hover::after,
.nav-link.active::after {
    width: 100%;
}

.nav-toggle {
    display: none;
    flex-direction: column;
    cursor: pointer;
}

.bar {
    width: 25px;
    height: 3px;
    background: var(--dark-color);
    margin: 3px 0;
    transition: 0.3s;
}

/* Hero Section */
.hero {
    min-height: 100vh;
    display: flex;
    align-items: center;
    position: relative;
    overflow: hidden;
}

.hero-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--gradient-hero);
    z-index: -2;
}

.hero-background::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:
        radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.2) 0%, transparent 50%);
    animation: gradientShift 8s ease-in-out infinite;
}

.hero-background::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="dots" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23dots)"/></svg>');
    animation: float 6s ease-in-out infinite;
}

@keyframes gradientShift {
    0%, 100% { opacity: 1; transform: scale(1); }
    50% { opacity: 0.8; transform: scale(1.05); }
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

.hero-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(15, 23, 42, 0.1);
    z-index: -1;
}

.hero-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
    color: var(--white-color);
    padding: 2rem;
    max-width: 1200px;
    margin: 0 auto;
}

.hero-text {
    animation: fadeInUp 1s ease-out;
}

.hero-title {
    margin-bottom: 1.5rem;
}

.title-main {
    display: block;
    font-size: 3.5rem;
    font-weight: 900;
    line-height: 1.2;
    margin-bottom: 0.5rem;
}

.title-sub {
    display: block;
    font-size: 1.5rem;
    font-weight: 400;
    opacity: 0.9;
    color: var(--secondary-color);
}

.hero-description {
    font-size: 1.2rem;
    margin-bottom: 2rem;
    opacity: 0.9;
    line-height: 1.8;
}

.hero-buttons {
    display: flex;
    gap: 1rem;
    margin-bottom: 2rem;
}

.btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 1rem 2rem;
    border: none;
    border-radius: var(--border-radius-full);
    font-weight: 600;
    text-decoration: none;
    cursor: pointer;
    transition: all var(--transition-normal);
    font-size: 1rem;
    position: relative;
    overflow: hidden;
    backdrop-filter: blur(10px);
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.btn:hover::before {
    left: 100%;
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--accent-color) 100%);
    color: var(--white-color);
    box-shadow: var(--shadow-colored);
}

.btn-primary:hover {
    transform: translateY(-3px) scale(1.02);
    box-shadow: var(--shadow-glow);
}

.btn-secondary {
    background: rgba(255, 255, 255, 0.1);
    color: var(--white-color);
    border: 2px solid rgba(255, 255, 255, 0.3);
    backdrop-filter: blur(10px);
}

.btn-secondary:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.5);
    transform: translateY(-3px) scale(1.02);
    box-shadow: var(--shadow-xl);
}

.hero-social {
    display: flex;
    gap: 1rem;
}

.social-link {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 50px;
    height: 50px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    color: var(--white-color);
    font-size: 1.2rem;
    text-decoration: none;
    transition: all var(--transition-normal);
    backdrop-filter: blur(10px);
}

.social-link:hover {
    background: var(--secondary-color);
    transform: translateY(-3px);
    box-shadow: var(--shadow-lg);
}

.hero-image {
    display: flex;
    justify-content: center;
    align-items: center;
    animation: fadeInRight 1s ease-out;
}

.hero-avatar {
    position: relative;
    width: 300px;
    height: 300px;
    margin: 0 auto;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, rgba(245, 158, 11, 0.1) 0%, rgba(245, 158, 11, 0.05) 100%);
    border-radius: 50%;
    padding: 10px;
}

.hero-avatar img {
    width: 100%;
    height: 100%;
    border-radius: 50%;
    object-fit: cover;
    object-position: center center;
    border: 5px solid rgba(255, 255, 255, 0.2);
    box-shadow: var(--shadow-xl);
    transition: all var(--transition-normal);
    filter: brightness(1.1) contrast(1.1);
    z-index: 2;
    position: relative;
}

.hero-avatar img:hover {
    transform: scale(1.05);
    filter: brightness(1.2) contrast(1.2);
    box-shadow: 0 25px 35px -5px rgba(0, 0, 0, 0.15), 0 15px 15px -5px rgba(0, 0, 0, 0.08);
}

.avatar-ring {
    position: absolute;
    top: -15px;
    left: -15px;
    right: -15px;
    bottom: -15px;
    border: 4px solid var(--secondary-color);
    border-radius: 50%;
    border-top-color: transparent;
    border-right-color: transparent;
    animation: rotate 10s linear infinite;
    z-index: 1;
}

.avatar-ring::before {
    content: '';
    position: absolute;
    top: -4px;
    left: -4px;
    right: -4px;
    bottom: -4px;
    border: 2px solid rgba(245, 158, 11, 0.3);
    border-radius: 50%;
    animation: rotate 15s linear infinite reverse;
}

.avatar-ring::after {
    content: '';
    position: absolute;
    top: -8px;
    left: -8px;
    right: -8px;
    bottom: -8px;
    border: 1px solid rgba(245, 158, 11, 0.15);
    border-radius: 50%;
    animation: rotate 20s linear infinite;
}

@keyframes rotate {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.scroll-indicator {
    position: absolute;
    bottom: 2rem;
    left: 50%;
    transform: translateX(-50%);
    animation: bounce 2s infinite;
}

.scroll-arrow {
    color: var(--white-color);
    font-size: 2rem;
    opacity: 0.7;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% { transform: translateX(-50%) translateY(0); }
    40% { transform: translateX(-50%) translateY(-10px); }
    60% { transform: translateX(-50%) translateY(-5px); }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* Section Styles */
section {
    padding: 5rem 0;
}

.section-header {
    text-align: center;
    margin-bottom: 4rem;
}

.section-title {
    font-size: 3rem;
    font-weight: 700;
    color: var(--dark-color);
    margin-bottom: 1rem;
}

.section-divider {
    width: 60px;
    height: 4px;
    background: var(--gradient-primary);
    margin: 0 auto 1rem;
    border-radius: 2px;
}

.section-subtitle {
    font-size: 1.2rem;
    color: var(--gray-color);
    max-width: 600px;
    margin: 0 auto;
}

/* About Section */
.about {
    padding: 5rem 0;
    background: var(--light-color);
}

.about-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: start;
}

.about-text h3 {
    font-size: 2rem;
    font-weight: 700;
    color: var(--dark-color);
    margin-bottom: 1.5rem;
}

.about-text p {
    font-size: 1.1rem;
    color: var(--gray-color);
    margin-bottom: 1.5rem;
    line-height: 1.8;
}

.about-stats {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 2rem;
    margin-top: 2rem;
}

.stat-item {
    text-align: center;
    padding: 1.5rem;
    background: var(--white-color);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-md);
}

.stat-number {
    display: block;
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

.stat-label {
    font-size: 1rem;
    color: var(--gray-color);
}

.about-skills h3 {
    font-size: 2rem;
    font-weight: 700;
    color: var(--dark-color);
    margin-bottom: 2rem;
}

.skills-grid {
    display: grid;
    gap: 2rem;
}

.skill-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1.5rem;
    background: var(--white-color);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-md);
}

.skill-icon {
    font-size: 2rem;
    color: var(--primary-color);
    min-width: 50px;
}

.skill-item h4 {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--dark-color);
    margin-bottom: 0.5rem;
}

.skill-bar {
    flex: 1;
    height: 6px;
    background: var(--light-color);
    border-radius: 3px;
    overflow: hidden;
}

.skill-progress {
    height: 100%;
    background: var(--gradient-primary);
    border-radius: 3px;
    width: 0;
    transition: width 1s ease-out;
}

/* Certificates & Achievements Section */
.certificates {
    padding: var(--spacing-3xl) 0;
    background: var(--gradient-card);
    position: relative;
}

.certificates::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="cert-pattern" width="50" height="50" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="2" fill="rgba(102, 126, 234, 0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23cert-pattern)"/></svg>');
    opacity: 0.5;
}

.certificates-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-xl);
    margin-bottom: var(--spacing-3xl);
}

.certificate-card {
    background: var(--white-color);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-xl);
    box-shadow: var(--shadow-lg);
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;
    border: 1px solid rgba(102, 126, 234, 0.1);
}

.certificate-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: var(--gradient-primary);
}

.certificate-card:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-2xl);
    border-color: var(--primary-color);
}

.certificate-icon {
    width: 60px;
    height: 60px;
    background: var(--gradient-primary);
    border-radius: var(--border-radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: var(--spacing-lg);
    color: var(--white-color);
    font-size: 1.5rem;
}

.certificate-content h3 {
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--dark-color);
    margin-bottom: var(--spacing-sm);
}

.certificate-issuer {
    color: var(--primary-color);
    font-weight: 600;
    margin-bottom: var(--spacing-xs);
}

.certificate-date {
    color: var(--gray-color);
    font-size: 0.9rem;
    margin-bottom: var(--spacing-lg);
}

.certificate-skills {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-sm);
}

.skill-tag {
    background: rgba(102, 126, 234, 0.1);
    color: var(--primary-color);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius-full);
    font-size: 0.8rem;
    font-weight: 500;
}

.achievements-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-xl);
    margin-top: var(--spacing-3xl);
}

.achievement-item {
    text-align: center;
    padding: var(--spacing-xl);
    background: var(--white-color);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-md);
    transition: all var(--transition-normal);
}

.achievement-item:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.achievement-number {
    font-size: 3rem;
    font-weight: 900;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: var(--spacing-sm);
}

.achievement-label {
    color: var(--gray-color);
    font-weight: 600;
    font-size: 1.1rem;
}

/* Services Section */
.services {
    padding: 5rem 0;
    background: var(--light-color);
}

.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
}

.service-card {
    position: relative;
    padding: 2rem;
    background: var(--white-color);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-md);
    transition: all var(--transition-normal);
    overflow: hidden;
    opacity: 1 !important;
    visibility: visible !important;
}

.service-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: var(--gradient-primary);
}

.service-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-xl);
}

.service-card.featured {
    border: 2px solid var(--primary-color);
    transform: scale(1.05);
}

.service-badge {
    position: absolute;
    top: 1rem;
    right: 1rem;
    background: var(--secondary-color);
    color: var(--white-color);
    padding: 0.5rem 1rem;
    border-radius: var(--border-radius);
    font-size: 0.8rem;
    font-weight: 600;
}

.service-icon {
    font-size: 3rem;
    color: var(--primary-color);
    margin-bottom: 1.5rem;
}

.service-card h3 {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--dark-color);
    margin-bottom: 1rem;
}

.service-card p {
    color: var(--gray-color);
    margin-bottom: 1.5rem;
    line-height: 1.6;
}

.service-features {
    list-style: none;
    margin-bottom: 1.5rem;
}

.service-features li {
    padding: 0.5rem 0;
    color: var(--gray-color);
    position: relative;
    padding-right: 1.5rem;
}

.service-features li::before {
    content: '✓';
    position: absolute;
    right: 0;
    color: var(--success-color);
    font-weight: bold;
}



/* Portfolio Section */
.portfolio {
    padding: 5rem 0;
    background: var(--light-color);
}

.portfolio-filters {
    display: flex;
    justify-content: center;
    gap: 1rem;
    margin-bottom: 3rem;
    flex-wrap: wrap;
}

.filter-btn {
    padding: 0.75rem 1.5rem;
    background: var(--white-color);
    border: 2px solid var(--primary-color);
    border-radius: var(--border-radius);
    color: var(--primary-color);
    font-weight: 600;
    cursor: pointer;
    transition: all var(--transition-normal);
}

.filter-btn:hover,
.filter-btn.active {
    background: var(--primary-color);
    color: var(--white-color);
}

.portfolio-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
    margin-bottom: 3rem;
}

.portfolio-item {
    position: relative;
    border-radius: var(--border-radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow-md);
    transition: all var(--transition-normal);
    background: var(--white-color);
    opacity: 1 !important;
    visibility: visible !important;
}

.portfolio-item:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-xl);
}

.portfolio-image {
    position: relative;
    width: 100%;
    height: 250px;
    overflow: hidden;
}

.portfolio-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform var(--transition-normal);
}

.portfolio-placeholder {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--gradient-primary);
    display: none;
    align-items: center;
    justify-content: center;
    color: var(--white-color);
    font-size: 3rem;
}

.portfolio-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: all var(--transition-normal);
    padding: var(--spacing-lg);
}

.portfolio-item:hover .portfolio-overlay {
    opacity: 1;
}

.portfolio-item:hover .portfolio-image img {
    transform: scale(1.1);
}

.portfolio-content {
    text-align: center;
    color: var(--white-color);
}

.portfolio-content h3 {
    font-size: 1.25rem;
    font-weight: 700;
    margin-bottom: var(--spacing-sm);
}

.portfolio-content p {
    font-size: 0.9rem;
    margin-bottom: var(--spacing-lg);
    opacity: 0.9;
}

.portfolio-tags {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: var(--spacing-xs);
    margin-bottom: var(--spacing-lg);
}

.portfolio-tags .tag {
    background: rgba(255, 255, 255, 0.2);
    color: var(--white-color);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius-full);
    font-size: 0.75rem;
    font-weight: 500;
}

.portfolio-actions {
    display: flex;
    justify-content: center;
    gap: var(--spacing-sm);
}

.portfolio-btn {
    width: 40px;
    height: 40px;
    border-radius: var(--border-radius-full);
    background: rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: var(--white-color);
    display: flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
    transition: all var(--transition-normal);
    cursor: pointer;
}

.portfolio-btn:hover {
    background: var(--primary-color);
    border-color: var(--primary-color);
    transform: scale(1.1);
}

.portfolio-icon-container {
    position: relative;
    width: 100%;
    height: 250px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, rgba(37, 99, 235, 0.05) 0%, rgba(139, 92, 246, 0.05) 100%);
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    transition: all var(--transition-normal);
}

.portfolio-icon-container:hover {
    background: linear-gradient(135deg, rgba(37, 99, 235, 0.1) 0%, rgba(139, 92, 246, 0.1) 100%);
}

.portfolio-icon {
    font-size: 4rem;
    transition: all var(--transition-normal);
    opacity: 0.8;
    position: relative;
    z-index: 1;
}

.portfolio-icon::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 120px;
    height: 120px;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
    border-radius: 50%;
    transform: translateX(-50%) translateY(-50%);
    transition: all var(--transition-normal);
    z-index: -1;
}

.portfolio-item:hover .portfolio-icon {
    transform: scale(1.1);
    opacity: 1;
    filter: drop-shadow(0 0 20px rgba(37, 99, 235, 0.3));
}

.portfolio-item:hover .portfolio-icon::before {
    width: 140px;
    height: 140px;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.15) 0%, transparent 70%);
}

/* خلفيات مخصصة لكل فئة */
.portfolio-item.brand .portfolio-icon-container {
    background: linear-gradient(135deg, rgba(245, 158, 11, 0.05) 0%, rgba(251, 191, 36, 0.05) 100%);
}

.portfolio-item.brand:hover .portfolio-icon-container {
    background: linear-gradient(135deg, rgba(245, 158, 11, 0.1) 0%, rgba(251, 191, 36, 0.1) 100%);
}

.portfolio-item.web .portfolio-icon-container {
    background: linear-gradient(135deg, rgba(37, 99, 235, 0.05) 0%, rgba(59, 130, 246, 0.05) 100%);
}

.portfolio-item.web:hover .portfolio-icon-container {
    background: linear-gradient(135deg, rgba(37, 99, 235, 0.1) 0%, rgba(59, 130, 246, 0.1) 100%);
}

.portfolio-item.app .portfolio-icon-container {
    background: linear-gradient(135deg, rgba(139, 92, 246, 0.05) 0%, rgba(167, 139, 250, 0.05) 100%);
}

.portfolio-item.app:hover .portfolio-icon-container {
    background: linear-gradient(135deg, rgba(139, 92, 246, 0.1) 0%, rgba(167, 139, 250, 0.1) 100%);
}

.portfolio-item.print .portfolio-icon-container {
    background: linear-gradient(135deg, rgba(16, 185, 129, 0.05) 0%, rgba(52, 211, 153, 0.05) 100%);
}

.portfolio-item.print:hover .portfolio-icon-container {
    background: linear-gradient(135deg, rgba(16, 185, 129, 0.1) 0%, rgba(52, 211, 153, 0.1) 100%);
}

.portfolio-content {
    padding: 1.5rem;
}

.portfolio-title {
    font-size: 1.3rem;
    font-weight: 700;
    color: var(--dark-color);
    margin-bottom: 0.5rem;
}

.portfolio-category {
    color: var(--primary-color);
    font-size: 0.9rem;
    font-weight: 600;
    margin-bottom: 1rem;
}

.portfolio-description {
    color: var(--gray-color);
    font-size: 0.95rem;
    line-height: 1.6;
    margin-bottom: 1rem;
}

.portfolio-tags {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.portfolio-tag {
    background: var(--light-color);
    color: var(--gray-color);
    padding: 0.25rem 0.75rem;
    border-radius: var(--border-radius);
    font-size: 0.8rem;
}

.portfolio-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(37, 99, 235, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity var(--transition-normal);
}

.portfolio-item:hover .portfolio-overlay {
    opacity: 1;
}

.portfolio-links {
    display: flex;
    gap: 1rem;
}

.portfolio-link {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 50px;
    height: 50px;
    background: var(--white-color);
    border-radius: 50%;
    color: var(--primary-color);
    font-size: 1.2rem;
    text-decoration: none;
    transition: all var(--transition-normal);
}

.portfolio-link:hover {
    background: var(--secondary-color);
    color: var(--white-color);
    transform: scale(1.1);
}

.portfolio-load-more {
    text-align: center;
}

/* Portfolio Modal */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(5px);
}

.modal.active {
    display: flex;
    align-items: center;
    justify-content: center;
    animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.modal-content {
    background: var(--white-color);
    border-radius: var(--border-radius-lg);
    max-width: 800px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
    position: relative;
    animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-xl);
    border-bottom: 1px solid var(--light-color);
}

.modal-header h3 {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--dark-color);
    margin: 0;
}

.modal-close {
    width: 40px;
    height: 40px;
    border-radius: var(--border-radius-full);
    background: var(--light-color);
    border: none;
    color: var(--gray-color);
    cursor: pointer;
    transition: all var(--transition-normal);
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-close:hover {
    background: var(--error-color);
    color: var(--white-color);
}

.modal-body {
    padding: var(--spacing-xl);
}

.modal-image {
    width: 100%;
    height: 300px;
    border-radius: var(--border-radius);
    overflow: hidden;
    margin-bottom: var(--spacing-xl);
}

.modal-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.modal-details p {
    font-size: 1.1rem;
    line-height: 1.6;
    color: var(--gray-color);
    margin-bottom: var(--spacing-lg);
}

.modal-info {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-xl);
}

.info-item {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
}

.info-item strong {
    color: var(--dark-color);
    font-weight: 600;
}

.info-item span {
    color: var(--gray-color);
}

.modal-tags {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-xl);
}

.modal-tags .tag {
    background: rgba(102, 126, 234, 0.1);
    color: var(--primary-color);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius-full);
    font-size: 0.875rem;
    font-weight: 500;
}

.modal-actions {
    text-align: center;
}

/* Client Reviews Section */
.reviews {
    padding: var(--spacing-3xl) 0;
    background: var(--dark-color);
    color: var(--white-color);
    position: relative;
    overflow: hidden;
}

.reviews::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--gradient-dark);
    opacity: 0.9;
}

.reviews::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="stars" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="rgba(255, 255, 255, 0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23stars)"/></svg>');
    animation: twinkle 4s ease-in-out infinite;
}

@keyframes twinkle {
    0%, 100% { opacity: 0.3; }
    50% { opacity: 0.6; }
}

.reviews .section-title,
.reviews .section-subtitle {
    color: var(--white-color);
}

.reviews-slider {
    position: relative;
    max-width: 800px;
    margin: 0 auto var(--spacing-xl);
    min-height: 300px;
}

.review-card {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    opacity: 0;
    transform: translateX(100px);
    transition: all var(--transition-slow);
    z-index: 1;
}

.review-card.active {
    opacity: 1;
    transform: translateX(0);
    z-index: 2;
}

.review-content {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-2xl);
    text-align: center;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.review-stars {
    display: flex;
    justify-content: center;
    gap: var(--spacing-xs);
    margin-bottom: var(--spacing-lg);
    color: var(--secondary-color);
    font-size: 1.2rem;
}

.review-text {
    font-size: 1.2rem;
    line-height: 1.8;
    margin-bottom: var(--spacing-xl);
    font-style: italic;
    color: var(--light-color);
}

.review-author {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-lg);
}

.author-avatar {
    position: relative;
    width: 60px;
    height: 60px;
}

.author-avatar img {
    width: 100%;
    height: 100%;
    border-radius: var(--border-radius-full);
    object-fit: cover;
    border: 3px solid var(--primary-color);
}

.avatar-placeholder {
    width: 100%;
    height: 100%;
    border-radius: var(--border-radius-full);
    background: var(--gradient-primary);
    display: none;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--white-color);
}

.author-info h4 {
    font-size: 1.1rem;
    font-weight: 700;
    margin-bottom: var(--spacing-xs);
    color: var(--white-color);
}

.author-info p {
    color: var(--gray-light);
    font-size: 0.9rem;
}

.reviews-navigation {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-xl);
}

.review-nav-btn {
    width: 50px;
    height: 50px;
    border-radius: var(--border-radius-full);
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: var(--white-color);
    cursor: pointer;
    transition: all var(--transition-normal);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
}

.review-nav-btn:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: scale(1.1);
}

.review-indicators {
    display: flex;
    gap: var(--spacing-sm);
}

.indicator {
    width: 12px;
    height: 12px;
    border-radius: var(--border-radius-full);
    background: rgba(255, 255, 255, 0.3);
    cursor: pointer;
    transition: all var(--transition-normal);
}

.indicator.active {
    background: var(--primary-color);
    transform: scale(1.2);
}

/* Contact Section */
.contact {
    padding: 5rem 0;
    background: var(--white-color);
}

.contact-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
}

.contact-info h3 {
    font-size: 2rem;
    font-weight: 700;
    color: var(--dark-color);
    margin-bottom: 2rem;
}

.contact-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1.5rem;
    background: var(--light-color);
    border-radius: var(--border-radius-lg);
    margin-bottom: 1.5rem;
    transition: all var(--transition-normal);
    opacity: 1 !important;
    visibility: visible !important;
    transform: none !important;
}

.contact-item:hover {
    background: var(--white-color);
    box-shadow: var(--shadow-md);
}

.contact-icon {
    font-size: 2rem;
    color: var(--primary-color);
    min-width: 60px;
    text-align: center;
}

.contact-details h4 {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--dark-color);
    margin-bottom: 0.5rem;
}

.contact-details p {
    color: var(--gray-color);
    margin-bottom: 0.5rem;
}

.contact-link {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 600;
    transition: color var(--transition-fast);
}

.contact-link:hover {
    color: var(--secondary-color);
}

.contact-form h3 {
    font-size: 2rem;
    font-weight: 700;
    color: var(--dark-color);
    margin-bottom: 2rem;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 1rem;
    border: 2px solid var(--light-color);
    border-radius: var(--border-radius);
    font-size: 1rem;
    font-family: inherit;
    transition: border-color var(--transition-fast);
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-color);
}

.form-group textarea {
    resize: vertical;
    min-height: 120px;
}

.form-group input.error,
.form-group select.error,
.form-group textarea.error {
    border-color: var(--error-color);
    background-color: rgba(239, 68, 68, 0.05);
}

.error-message {
    color: var(--error-color);
    font-size: 0.875rem;
    margin-top: var(--spacing-xs);
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
}

.error-message::before {
    content: '⚠';
    font-size: 1rem;
}

/* AI Assistant */
.ai-assistant {
    position: fixed;
    bottom: 2rem;
    right: 2rem;
    z-index: 1000;
}

.ai-toggle {
    width: 60px;
    height: 60px;
    background: var(--gradient-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--white-color);
    font-size: 1.5rem;
    cursor: pointer;
    box-shadow: var(--shadow-lg);
    transition: all var(--transition-normal);
}

.ai-toggle:hover {
    transform: scale(1.1);
    box-shadow: var(--shadow-xl);
}

.ai-chat {
    position: absolute;
    bottom: 80px;
    right: 0;
    width: 350px;
    height: 450px;
    background: var(--white-color);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-xl);
    display: none;
    flex-direction: column;
    overflow: hidden;
}

.ai-chat.active {
    display: flex;
}

.ai-header {
    background: var(--gradient-primary);
    color: var(--white-color);
    padding: 1rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.ai-header h3 {
    font-size: 1.2rem;
    font-weight: 600;
}

.ai-close {
    background: none;
    border: none;
    color: var(--white-color);
    font-size: 1.2rem;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 50%;
    transition: background var(--transition-fast);
}

.ai-close:hover {
    background: rgba(255, 255, 255, 0.2);
}

.ai-messages {
    flex: 1;
    padding: 1rem;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.ai-message {
    display: flex;
    align-items: flex-start;
    gap: 0.5rem;
}

.ai-message.bot {
    flex-direction: row;
}

.ai-message.user {
    flex-direction: row-reverse;
}

.message-content {
    max-width: 80%;
    padding: 0.75rem 1rem;
    border-radius: var(--border-radius);
    font-size: 0.9rem;
    line-height: 1.4;
}

.ai-message.bot .message-content {
    background: var(--light-color);
    color: var(--dark-color);
}

.ai-message.user .message-content {
    background: var(--primary-color);
    color: var(--white-color);
}

.ai-input {
    padding: 1rem;
    border-top: 1px solid var(--light-color);
    display: flex;
    gap: 0.5rem;
}

.ai-input input {
    flex: 1;
    padding: 0.75rem;
    border: 1px solid var(--light-color);
    border-radius: var(--border-radius);
    font-size: 0.9rem;
}

.ai-input input:focus {
    outline: none;
    border-color: var(--primary-color);
}

.ai-input button {
    padding: 0.75rem 1rem;
    background: var(--primary-color);
    color: var(--white-color);
    border: none;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: background var(--transition-fast);
}

.ai-input button:hover {
    background: #1d4ed8;
}

/* Update Notification */
.update-notification {
    position: fixed;
    top: 20px;
    right: 20px;
    background: var(--primary-color);
    color: var(--white-color);
    padding: 1rem;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-lg);
    z-index: 10000;
    font-family: 'Cairo', sans-serif;
    max-width: 300px;
    animation: slideInRight 0.3s ease-out;
}

.update-content {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.update-btn {
    background: var(--white-color);
    color: var(--primary-color);
    border: none;
    padding: 0.5rem 1rem;
    border-radius: var(--border-radius);
    font-size: 0.9rem;
    font-weight: 600;
    cursor: pointer;
    transition: all var(--transition-fast);
}

.update-btn:hover {
    background: var(--light-color);
    transform: translateY(-1px);
}

.close-btn {
    background: none;
    border: none;
    color: var(--white-color);
    font-size: 1.2rem;
    cursor: pointer;
    padding: 0.25rem;
    margin-left: auto;
    opacity: 0.8;
    transition: opacity var(--transition-fast);
}

.close-btn:hover {
    opacity: 1;
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Footer */
.footer {
    background: var(--dark-color);
    color: var(--white-color);
    padding: 3rem 0 1rem;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.footer-section h4 {
    font-size: 1.3rem;
    font-weight: 700;
    margin-bottom: 1rem;
    color: var(--secondary-color);
}

.footer-logo {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1rem;
}

.footer-logo-m {
    width: 55px !important;
    height: 55px !important;
    font-size: 2rem !important;
    border-radius: 15px !important;
}

.footer-logo h3 {
    font-size: 1.5rem;
    font-weight: 700;
}

.footer-section p {
    color: var(--gray-color);
    margin-bottom: 1.5rem;
    line-height: 1.6;
}

.footer-social {
    display: flex;
    gap: 1rem;
}

.footer-social a {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background: var(--primary-color);
    border-radius: 50%;
    color: var(--white-color);
    text-decoration: none;
    transition: all var(--transition-normal);
}

.footer-social a:hover {
    background: var(--secondary-color);
    transform: translateY(-2px);
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: 0.5rem;
}

.footer-section ul li a {
    color: var(--gray-color);
    text-decoration: none;
    transition: color var(--transition-fast);
}

.footer-section ul li a:hover {
    color: var(--secondary-color);
}

.footer-contact p {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.5rem;
    color: var(--gray-color);
}

.footer-contact i {
    color: var(--primary-color);
    min-width: 20px;
}

.footer-bottom {
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    padding-top: 2rem;
    text-align: center;
    color: var(--gray-color);
}

/* Responsive Design */
@media (max-width: 768px) {
    .nav-menu {
        position: fixed;
        top: 70px;
        left: -100%;
        width: 100%;
        height: calc(100vh - 70px);
        background: var(--white-color);
        flex-direction: column;
        align-items: center;
        justify-content: flex-start;
        padding-top: 2rem;
        transition: left var(--transition-normal);
        box-shadow: var(--shadow-lg);
    }

    .nav-menu.active {
        left: 0;
    }

    .nav-toggle {
        display: flex;
    }

    .nav-toggle.active .bar:nth-child(2) {
        opacity: 0;
    }

    .nav-toggle.active .bar:nth-child(1) {
        transform: translateY(8px) rotate(45deg);
    }

    .nav-toggle.active .bar:nth-child(3) {
        transform: translateY(-8px) rotate(-45deg);
    }

    .logo-m {
        width: 40px;
        height: 40px;
        font-size: 1.6rem;
        border-radius: 10px;
    }

    .logo-text {
        font-size: 1.3rem;
    }

    .footer-logo-m {
        width: 50px !important;
        height: 50px !important;
        font-size: 1.8rem !important;
        border-radius: 12px !important;
    }

    .hero-content {
        grid-template-columns: 1fr;
        text-align: center;
        gap: 2rem;
    }

    .title-main {
        font-size: 2.5rem;
    }

    .hero-buttons {
        justify-content: center;
        flex-wrap: wrap;
    }

    .about-content,
    .contact-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .about-stats {
        grid-template-columns: 1fr;
    }

    .services-grid {
        grid-template-columns: 1fr;
    }

    .portfolio-grid {
        grid-template-columns: 1fr;
    }

    .section-title {
        font-size: 2.5rem;
    }

    .hero-avatar {
        width: 250px;
        height: 250px;
        margin: 2rem auto;
    }
    
    .avatar-ring {
        top: -12px;
        left: -12px;
        right: -12px;
        bottom: -12px;
        border-width: 3px;
    }

    .ai-chat {
        width: 300px;
        height: 400px;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 0 15px;
    }

    .nav-container {
        padding: 1rem 1.5rem;
    }

    .hero-content {
        padding: 1rem;
    }

    .title-main {
        font-size: 2rem;
    }

    .title-sub {
        font-size: 1.2rem;
    }

    .hero-description {
        font-size: 1rem;
    }

    .section-title {
        font-size: 2rem;
    }

    .hero-avatar {
        width: 200px;
        height: 200px;
        margin: 1.5rem auto;
    }
    
    .avatar-ring {
        top: -10px;
        left: -10px;
        right: -10px;
        bottom: -10px;
        border-width: 2px;
    }

    .ai-assistant {
        bottom: 1rem;
        right: 1rem;
    }

    .ai-chat {
        width: 280px;
        height: 350px;
    }

    .btn {
        padding: 0.6rem 1.2rem;
        font-size: 0.9rem;
    }
}

/* Animations */
.fade-in {
    animation: fadeIn 0.6s ease-out;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

/* Critical Elements - Always Visible */
.hero-text,
.hero-image,
.service-card,
.contact-item,
.contact-info,
.contact-form {
    opacity: 1 !important;
    visibility: visible !important;
}

.slide-in-up {
    animation: slideInUp 0.6s ease-out;
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.scale-in {
    animation: scaleIn 0.6s ease-out;
}

@keyframes scaleIn {
    from {
        opacity: 0;
        transform: scale(0.9);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

/* Utility Classes */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.mb-1 { margin-bottom: 0.5rem; }
.mb-2 { margin-bottom: 1rem; }
.mb-3 { margin-bottom: 1.5rem; }
.mb-4 { margin-bottom: 2rem; }

.mt-1 { margin-top: 0.5rem; }
.mt-2 { margin-top: 1rem; }
.mt-3 { margin-top: 1.5rem; }
.mt-4 { margin-top: 2rem; }

.hidden { display: none; }
.visible { display: block; }

/* Print Styles */
@media print {
    .navbar,
    .ai-assistant,
    .scroll-indicator,
    .hero-buttons,
    .hero-social,
    .contact-form {
        display: none;
    }
    
    body {
        color: black;
        background: white;
    }
    
    .hero-background {
        display: none;
    }
    
    .hero-content {
        color: black;
    }

    /* Certificates Section Mobile */
    .certificates-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-lg);
    }

    .certificate-card {
        padding: var(--spacing-lg);
    }

    .achievements-stats {
        grid-template-columns: repeat(2, 1fr);
        gap: var(--spacing-lg);
    }

    .achievement-number {
        font-size: 2rem;
    }

    /* Reviews Section Mobile */
    .reviews-slider {
        min-height: 250px;
    }

    .review-content {
        padding: var(--spacing-lg);
    }

    .review-text {
        font-size: 1rem;
    }

    .review-author {
        flex-direction: column;
        gap: var(--spacing-sm);
        text-align: center;
    }

    .author-avatar {
        width: 50px;
        height: 50px;
    }

    .reviews-navigation {
        gap: var(--spacing-lg);
    }

    .review-nav-btn {
        width: 40px;
        height: 40px;
        font-size: 1rem;
    }

    /* Portfolio Modal Mobile */
    .modal-content {
        width: 95%;
        max-height: 95vh;
        margin: 2.5vh auto;
    }

    .modal-header {
        padding: var(--spacing-lg);
    }

    .modal-body {
        padding: var(--spacing-lg);
    }

    .modal-image {
        height: 200px;
    }

    .modal-info {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
    }

    .modal-tags {
        justify-content: flex-start;
    }

    /* Touch-friendly buttons */
    .portfolio-btn,
    .review-nav-btn,
    .filter-btn {
        min-height: 44px;
        min-width: 44px;
    }

    /* Improve text readability on mobile */
    .section-title {
        line-height: 1.2;
    }

    .section-subtitle {
        font-size: 1rem;
        line-height: 1.5;
    }

    /* Optimize form for mobile */
    .form-group input,
    .form-group select,
    .form-group textarea {
        font-size: 16px; /* Prevent zoom on iOS */
    }
}

/* Accessibility Improvements */
/* Focus styles for keyboard navigation */
button:focus,
input:focus,
select:focus,
textarea:focus,
a:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

/* Skip to main content link */
.skip-link {
    position: absolute;
    top: -40px;
    left: 6px;
    background: var(--primary-color);
    color: var(--white-color);
    padding: 8px;
    text-decoration: none;
    border-radius: 4px;
    z-index: 1000;
}

.skip-link:focus {
    top: 6px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    :root {
        --primary-color: #0000ff;
        --secondary-color: #ff0000;
        --dark-color: #000000;
        --light-color: #ffffff;
        --gray-color: #666666;
    }
}

/* Screen reader only content */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* Ensure sufficient color contrast */
.portfolio-overlay,
.modal {
    background-color: rgba(0, 0, 0, 0.9);
}

/* Improve button accessibility */
button[disabled] {
    opacity: 0.6;
    cursor: not-allowed;
}

/* Focus indicators for interactive elements */
.portfolio-item:focus-within,
.certificate-card:focus-within,
.service-card:focus-within {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}
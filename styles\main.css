/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    --primary-color: #2563eb;
    --secondary-color: #f59e0b;
    --accent-color: #8b5cf6;
    --dark-color: #1f2937;
    --light-color: #f8fafc;
    --white-color: #ffffff;
    --gray-color: #6b7280;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --error-color: #ef4444;
    
    --gradient-primary: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%);
    --gradient-secondary: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    --gradient-accent: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    
    --transition-fast: 0.15s ease-in-out;
    --transition-normal: 0.3s ease-in-out;
    --transition-slow: 0.5s ease-in-out;
    
    --border-radius: 8px;
    --border-radius-lg: 12px;
    --border-radius-xl: 16px;
}

html {
    scroll-behavior: smooth;
    font-size: 16px;
}

body {
    font-family: 'Cairo', sans-serif;
    line-height: 1.6;
    color: var(--dark-color);
    background-color: var(--white-color);
    overflow-x: hidden;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Loading Screen */
#loading-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--gradient-primary);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
    transition: opacity 0.5s ease, visibility 0.5s ease;
}

#loading-screen.hidden {
    opacity: 0;
    visibility: hidden;
}

.loader {
    text-align: center;
    color: var(--white-color);
}

.loader-circle {
    width: 60px;
    height: 60px;
    border: 4px solid rgba(255, 255, 255, 0.3);
    border-top: 4px solid var(--white-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 20px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loader h3 {
    font-size: 1.5rem;
    margin-bottom: 10px;
    font-weight: 700;
}

.loader p {
    font-size: 1rem;
    opacity: 0.8;
}

/* Navigation */
.navbar {
    position: fixed;
    top: 0;
    width: 100%;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    z-index: 1000;
    transition: all var(--transition-normal);
}

.navbar.scrolled {
    background: rgba(255, 255, 255, 0.98);
    box-shadow: var(--shadow-md);
}

.nav-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 2rem;
    max-width: 1200px;
    margin: 0 auto;
}

.nav-logo {
    display: flex;
    align-items: center;
    gap: 10px;
}

.logo-m {
    width: 45px;
    height: 45px;
    background: var(--gradient-primary);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.8rem;
    font-weight: 900;
    color: var(--white-color);
    font-family: 'Cairo', sans-serif;
    text-transform: uppercase;
    letter-spacing: -1px;
    box-shadow: var(--shadow-md);
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.logo-m::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(45deg, rgba(255, 255, 255, 0.1) 0%, transparent 50%, rgba(255, 255, 255, 0.1) 100%);
    transform: translateX(-100%);
    transition: transform 0.6s ease;
}

.nav-logo:hover .logo-m::before {
    transform: translateX(100%);
}

.nav-logo:hover .logo-m {
    transform: translateY(-2px) scale(1.05);
    box-shadow: var(--shadow-lg);
}

.logo-text {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--primary-color);
}

.nav-menu {
    display: flex;
    gap: 2rem;
}

.nav-link {
    text-decoration: none;
    color: var(--dark-color);
    font-weight: 500;
    position: relative;
    transition: color var(--transition-fast);
}

.nav-link:hover,
.nav-link.active {
    color: var(--primary-color);
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--primary-color);
    transition: width var(--transition-normal);
}

.nav-link:hover::after,
.nav-link.active::after {
    width: 100%;
}

.nav-toggle {
    display: none;
    flex-direction: column;
    cursor: pointer;
}

.bar {
    width: 25px;
    height: 3px;
    background: var(--dark-color);
    margin: 3px 0;
    transition: 0.3s;
}

/* Hero Section */
.hero {
    min-height: 100vh;
    display: flex;
    align-items: center;
    position: relative;
    overflow: hidden;
}

.hero-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--gradient-primary);
    z-index: -2;
}

.hero-background::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="0.5" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    animation: grain 8s linear infinite;
}

@keyframes grain {
    0%, 100% { transform: translate(0, 0); }
    10% { transform: translate(-5%, -5%); }
    20% { transform: translate(-10%, 5%); }
    30% { transform: translate(5%, -10%); }
    40% { transform: translate(-5%, 15%); }
    50% { transform: translate(-10%, 5%); }
    60% { transform: translate(15%, 0%); }
    70% { transform: translate(0%, 10%); }
    80% { transform: translate(-15%, 0%); }
    90% { transform: translate(10%, 5%); }
}

.hero-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.2);
    z-index: -1;
}

.hero-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
    color: var(--white-color);
    padding: 2rem;
    max-width: 1200px;
    margin: 0 auto;
}

.hero-text {
    animation: fadeInUp 1s ease-out;
}

.hero-title {
    margin-bottom: 1.5rem;
}

.title-main {
    display: block;
    font-size: 3.5rem;
    font-weight: 900;
    line-height: 1.2;
    margin-bottom: 0.5rem;
}

.title-sub {
    display: block;
    font-size: 1.5rem;
    font-weight: 400;
    opacity: 0.9;
    color: var(--secondary-color);
}

.hero-description {
    font-size: 1.2rem;
    margin-bottom: 2rem;
    opacity: 0.9;
    line-height: 1.8;
}

.hero-buttons {
    display: flex;
    gap: 1rem;
    margin-bottom: 2rem;
}

.btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: var(--border-radius);
    font-weight: 600;
    text-decoration: none;
    cursor: pointer;
    transition: all var(--transition-normal);
    font-size: 1rem;
}

.btn-primary {
    background: var(--secondary-color);
    color: var(--white-color);
}

.btn-primary:hover {
    background: #d97706;
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.btn-secondary {
    background: transparent;
    color: var(--white-color);
    border: 2px solid var(--white-color);
}

.btn-secondary:hover {
    background: var(--white-color);
    color: var(--primary-color);
    transform: translateY(-2px);
}

.hero-social {
    display: flex;
    gap: 1rem;
}

.social-link {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 50px;
    height: 50px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    color: var(--white-color);
    font-size: 1.2rem;
    text-decoration: none;
    transition: all var(--transition-normal);
    backdrop-filter: blur(10px);
}

.social-link:hover {
    background: var(--secondary-color);
    transform: translateY(-3px);
    box-shadow: var(--shadow-lg);
}

.hero-image {
    display: flex;
    justify-content: center;
    align-items: center;
    animation: fadeInRight 1s ease-out;
}

.hero-avatar {
    position: relative;
    width: 300px;
    height: 300px;
    margin: 0 auto;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, rgba(245, 158, 11, 0.1) 0%, rgba(245, 158, 11, 0.05) 100%);
    border-radius: 50%;
    padding: 10px;
}

.hero-avatar img {
    width: 100%;
    height: 100%;
    border-radius: 50%;
    object-fit: cover;
    object-position: center center;
    border: 5px solid rgba(255, 255, 255, 0.2);
    box-shadow: var(--shadow-xl);
    transition: all var(--transition-normal);
    filter: brightness(1.1) contrast(1.1);
    z-index: 2;
    position: relative;
}

.hero-avatar img:hover {
    transform: scale(1.05);
    filter: brightness(1.2) contrast(1.2);
    box-shadow: 0 25px 35px -5px rgba(0, 0, 0, 0.15), 0 15px 15px -5px rgba(0, 0, 0, 0.08);
}

.avatar-ring {
    position: absolute;
    top: -15px;
    left: -15px;
    right: -15px;
    bottom: -15px;
    border: 4px solid var(--secondary-color);
    border-radius: 50%;
    border-top-color: transparent;
    border-right-color: transparent;
    animation: rotate 10s linear infinite;
    z-index: 1;
}

.avatar-ring::before {
    content: '';
    position: absolute;
    top: -4px;
    left: -4px;
    right: -4px;
    bottom: -4px;
    border: 2px solid rgba(245, 158, 11, 0.3);
    border-radius: 50%;
    animation: rotate 15s linear infinite reverse;
}

.avatar-ring::after {
    content: '';
    position: absolute;
    top: -8px;
    left: -8px;
    right: -8px;
    bottom: -8px;
    border: 1px solid rgba(245, 158, 11, 0.15);
    border-radius: 50%;
    animation: rotate 20s linear infinite;
}

@keyframes rotate {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.scroll-indicator {
    position: absolute;
    bottom: 2rem;
    left: 50%;
    transform: translateX(-50%);
    animation: bounce 2s infinite;
}

.scroll-arrow {
    color: var(--white-color);
    font-size: 2rem;
    opacity: 0.7;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% { transform: translateX(-50%) translateY(0); }
    40% { transform: translateX(-50%) translateY(-10px); }
    60% { transform: translateX(-50%) translateY(-5px); }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* Section Styles */
section {
    padding: 5rem 0;
}

.section-header {
    text-align: center;
    margin-bottom: 4rem;
}

.section-title {
    font-size: 3rem;
    font-weight: 700;
    color: var(--dark-color);
    margin-bottom: 1rem;
}

.section-divider {
    width: 60px;
    height: 4px;
    background: var(--gradient-primary);
    margin: 0 auto 1rem;
    border-radius: 2px;
}

.section-subtitle {
    font-size: 1.2rem;
    color: var(--gray-color);
    max-width: 600px;
    margin: 0 auto;
}

/* About Section */
.about {
    background: var(--light-color);
}

.about-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: start;
}

.about-text h3 {
    font-size: 2rem;
    font-weight: 700;
    color: var(--dark-color);
    margin-bottom: 1.5rem;
}

.about-text p {
    font-size: 1.1rem;
    color: var(--gray-color);
    margin-bottom: 1.5rem;
    line-height: 1.8;
}

.about-stats {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 2rem;
    margin-top: 2rem;
}

.stat-item {
    text-align: center;
    padding: 1.5rem;
    background: var(--white-color);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-md);
}

.stat-number {
    display: block;
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

.stat-label {
    font-size: 1rem;
    color: var(--gray-color);
}

.about-skills h3 {
    font-size: 2rem;
    font-weight: 700;
    color: var(--dark-color);
    margin-bottom: 2rem;
}

.skills-grid {
    display: grid;
    gap: 2rem;
}

.skill-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1.5rem;
    background: var(--white-color);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-md);
}

.skill-icon {
    font-size: 2rem;
    color: var(--primary-color);
    min-width: 50px;
}

.skill-item h4 {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--dark-color);
    margin-bottom: 0.5rem;
}

.skill-bar {
    flex: 1;
    height: 6px;
    background: var(--light-color);
    border-radius: 3px;
    overflow: hidden;
}

.skill-progress {
    height: 100%;
    background: var(--gradient-primary);
    border-radius: 3px;
    width: 0;
    transition: width 1s ease-out;
}

/* Services Section */
.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
}

.service-card {
    position: relative;
    padding: 2rem;
    background: var(--white-color);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-md);
    transition: all var(--transition-normal);
    overflow: hidden;
}

.service-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: var(--gradient-primary);
}

.service-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-xl);
}

.service-card.featured {
    border: 2px solid var(--primary-color);
    transform: scale(1.05);
}

.service-badge {
    position: absolute;
    top: 1rem;
    right: 1rem;
    background: var(--secondary-color);
    color: var(--white-color);
    padding: 0.5rem 1rem;
    border-radius: var(--border-radius);
    font-size: 0.8rem;
    font-weight: 600;
}

.service-icon {
    font-size: 3rem;
    color: var(--primary-color);
    margin-bottom: 1.5rem;
}

.service-card h3 {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--dark-color);
    margin-bottom: 1rem;
}

.service-card p {
    color: var(--gray-color);
    margin-bottom: 1.5rem;
    line-height: 1.6;
}

.service-features {
    list-style: none;
    margin-bottom: 2rem;
}

.service-features li {
    padding: 0.5rem 0;
    color: var(--gray-color);
    position: relative;
    padding-right: 1.5rem;
}

.service-features li::before {
    content: '✓';
    position: absolute;
    right: 0;
    color: var(--success-color);
    font-weight: bold;
}

.service-price {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 1rem;
    background: var(--light-color);
    border-radius: var(--border-radius);
}

.service-price span {
    font-size: 0.9rem;
    color: var(--gray-color);
}

.service-price strong {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--primary-color);
}

/* Portfolio Section */
.portfolio {
    background: var(--light-color);
}

.portfolio-filters {
    display: flex;
    justify-content: center;
    gap: 1rem;
    margin-bottom: 3rem;
    flex-wrap: wrap;
}

.filter-btn {
    padding: 0.75rem 1.5rem;
    background: var(--white-color);
    border: 2px solid var(--primary-color);
    border-radius: var(--border-radius);
    color: var(--primary-color);
    font-weight: 600;
    cursor: pointer;
    transition: all var(--transition-normal);
}

.filter-btn:hover,
.filter-btn.active {
    background: var(--primary-color);
    color: var(--white-color);
}

.portfolio-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
    margin-bottom: 3rem;
}

.portfolio-item {
    position: relative;
    border-radius: var(--border-radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow-md);
    transition: all var(--transition-normal);
    background: var(--white-color);
}

.portfolio-item:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-xl);
}

.portfolio-image {
    width: 100%;
    height: 250px;
    object-fit: cover;
    transition: transform var(--transition-normal);
}

.portfolio-item:hover .portfolio-image {
    transform: scale(1.05);
}

.portfolio-icon-container {
    position: relative;
    width: 100%;
    height: 250px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, rgba(37, 99, 235, 0.05) 0%, rgba(139, 92, 246, 0.05) 100%);
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    transition: all var(--transition-normal);
}

.portfolio-icon-container:hover {
    background: linear-gradient(135deg, rgba(37, 99, 235, 0.1) 0%, rgba(139, 92, 246, 0.1) 100%);
}

.portfolio-icon {
    font-size: 4rem;
    transition: all var(--transition-normal);
    opacity: 0.8;
    position: relative;
    z-index: 1;
}

.portfolio-icon::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 120px;
    height: 120px;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
    border-radius: 50%;
    transform: translateX(-50%) translateY(-50%);
    transition: all var(--transition-normal);
    z-index: -1;
}

.portfolio-item:hover .portfolio-icon {
    transform: scale(1.1);
    opacity: 1;
    filter: drop-shadow(0 0 20px rgba(37, 99, 235, 0.3));
}

.portfolio-item:hover .portfolio-icon::before {
    width: 140px;
    height: 140px;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.15) 0%, transparent 70%);
}

/* خلفيات مخصصة لكل فئة */
.portfolio-item.brand .portfolio-icon-container {
    background: linear-gradient(135deg, rgba(245, 158, 11, 0.05) 0%, rgba(251, 191, 36, 0.05) 100%);
}

.portfolio-item.brand:hover .portfolio-icon-container {
    background: linear-gradient(135deg, rgba(245, 158, 11, 0.1) 0%, rgba(251, 191, 36, 0.1) 100%);
}

.portfolio-item.web .portfolio-icon-container {
    background: linear-gradient(135deg, rgba(37, 99, 235, 0.05) 0%, rgba(59, 130, 246, 0.05) 100%);
}

.portfolio-item.web:hover .portfolio-icon-container {
    background: linear-gradient(135deg, rgba(37, 99, 235, 0.1) 0%, rgba(59, 130, 246, 0.1) 100%);
}

.portfolio-item.app .portfolio-icon-container {
    background: linear-gradient(135deg, rgba(139, 92, 246, 0.05) 0%, rgba(167, 139, 250, 0.05) 100%);
}

.portfolio-item.app:hover .portfolio-icon-container {
    background: linear-gradient(135deg, rgba(139, 92, 246, 0.1) 0%, rgba(167, 139, 250, 0.1) 100%);
}

.portfolio-item.print .portfolio-icon-container {
    background: linear-gradient(135deg, rgba(16, 185, 129, 0.05) 0%, rgba(52, 211, 153, 0.05) 100%);
}

.portfolio-item.print:hover .portfolio-icon-container {
    background: linear-gradient(135deg, rgba(16, 185, 129, 0.1) 0%, rgba(52, 211, 153, 0.1) 100%);
}

.portfolio-content {
    padding: 1.5rem;
}

.portfolio-title {
    font-size: 1.3rem;
    font-weight: 700;
    color: var(--dark-color);
    margin-bottom: 0.5rem;
}

.portfolio-category {
    color: var(--primary-color);
    font-size: 0.9rem;
    font-weight: 600;
    margin-bottom: 1rem;
}

.portfolio-description {
    color: var(--gray-color);
    font-size: 0.95rem;
    line-height: 1.6;
    margin-bottom: 1rem;
}

.portfolio-tags {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.portfolio-tag {
    background: var(--light-color);
    color: var(--gray-color);
    padding: 0.25rem 0.75rem;
    border-radius: var(--border-radius);
    font-size: 0.8rem;
}

.portfolio-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(37, 99, 235, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity var(--transition-normal);
}

.portfolio-item:hover .portfolio-overlay {
    opacity: 1;
}

.portfolio-links {
    display: flex;
    gap: 1rem;
}

.portfolio-link {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 50px;
    height: 50px;
    background: var(--white-color);
    border-radius: 50%;
    color: var(--primary-color);
    font-size: 1.2rem;
    text-decoration: none;
    transition: all var(--transition-normal);
}

.portfolio-link:hover {
    background: var(--secondary-color);
    color: var(--white-color);
    transform: scale(1.1);
}

.portfolio-load-more {
    text-align: center;
}

/* Contact Section */
.contact-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
}

.contact-info h3 {
    font-size: 2rem;
    font-weight: 700;
    color: var(--dark-color);
    margin-bottom: 2rem;
}

.contact-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1.5rem;
    background: var(--light-color);
    border-radius: var(--border-radius-lg);
    margin-bottom: 1.5rem;
    transition: all var(--transition-normal);
}

.contact-item:hover {
    background: var(--white-color);
    box-shadow: var(--shadow-md);
}

.contact-icon {
    font-size: 2rem;
    color: var(--primary-color);
    min-width: 60px;
    text-align: center;
}

.contact-details h4 {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--dark-color);
    margin-bottom: 0.5rem;
}

.contact-details p {
    color: var(--gray-color);
    margin-bottom: 0.5rem;
}

.contact-link {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 600;
    transition: color var(--transition-fast);
}

.contact-link:hover {
    color: var(--secondary-color);
}

.contact-form h3 {
    font-size: 2rem;
    font-weight: 700;
    color: var(--dark-color);
    margin-bottom: 2rem;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 1rem;
    border: 2px solid var(--light-color);
    border-radius: var(--border-radius);
    font-size: 1rem;
    font-family: inherit;
    transition: border-color var(--transition-fast);
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-color);
}

.form-group textarea {
    resize: vertical;
    min-height: 120px;
}

/* AI Assistant */
.ai-assistant {
    position: fixed;
    bottom: 2rem;
    right: 2rem;
    z-index: 1000;
}

.ai-toggle {
    width: 60px;
    height: 60px;
    background: var(--gradient-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--white-color);
    font-size: 1.5rem;
    cursor: pointer;
    box-shadow: var(--shadow-lg);
    transition: all var(--transition-normal);
}

.ai-toggle:hover {
    transform: scale(1.1);
    box-shadow: var(--shadow-xl);
}

.ai-chat {
    position: absolute;
    bottom: 80px;
    right: 0;
    width: 350px;
    height: 450px;
    background: var(--white-color);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-xl);
    display: none;
    flex-direction: column;
    overflow: hidden;
}

.ai-chat.active {
    display: flex;
}

.ai-header {
    background: var(--gradient-primary);
    color: var(--white-color);
    padding: 1rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.ai-header h3 {
    font-size: 1.2rem;
    font-weight: 600;
}

.ai-close {
    background: none;
    border: none;
    color: var(--white-color);
    font-size: 1.2rem;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 50%;
    transition: background var(--transition-fast);
}

.ai-close:hover {
    background: rgba(255, 255, 255, 0.2);
}

.ai-messages {
    flex: 1;
    padding: 1rem;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.ai-message {
    display: flex;
    align-items: flex-start;
    gap: 0.5rem;
}

.ai-message.bot {
    flex-direction: row;
}

.ai-message.user {
    flex-direction: row-reverse;
}

.message-content {
    max-width: 80%;
    padding: 0.75rem 1rem;
    border-radius: var(--border-radius);
    font-size: 0.9rem;
    line-height: 1.4;
}

.ai-message.bot .message-content {
    background: var(--light-color);
    color: var(--dark-color);
}

.ai-message.user .message-content {
    background: var(--primary-color);
    color: var(--white-color);
}

.ai-input {
    padding: 1rem;
    border-top: 1px solid var(--light-color);
    display: flex;
    gap: 0.5rem;
}

.ai-input input {
    flex: 1;
    padding: 0.75rem;
    border: 1px solid var(--light-color);
    border-radius: var(--border-radius);
    font-size: 0.9rem;
}

.ai-input input:focus {
    outline: none;
    border-color: var(--primary-color);
}

.ai-input button {
    padding: 0.75rem 1rem;
    background: var(--primary-color);
    color: var(--white-color);
    border: none;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: background var(--transition-fast);
}

.ai-input button:hover {
    background: #1d4ed8;
}

/* Footer */
.footer {
    background: var(--dark-color);
    color: var(--white-color);
    padding: 3rem 0 1rem;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.footer-section h4 {
    font-size: 1.3rem;
    font-weight: 700;
    margin-bottom: 1rem;
    color: var(--secondary-color);
}

.footer-logo {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1rem;
}

.footer-logo-m {
    width: 55px !important;
    height: 55px !important;
    font-size: 2rem !important;
    border-radius: 15px !important;
}

.footer-logo h3 {
    font-size: 1.5rem;
    font-weight: 700;
}

.footer-section p {
    color: var(--gray-color);
    margin-bottom: 1.5rem;
    line-height: 1.6;
}

.footer-social {
    display: flex;
    gap: 1rem;
}

.footer-social a {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background: var(--primary-color);
    border-radius: 50%;
    color: var(--white-color);
    text-decoration: none;
    transition: all var(--transition-normal);
}

.footer-social a:hover {
    background: var(--secondary-color);
    transform: translateY(-2px);
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: 0.5rem;
}

.footer-section ul li a {
    color: var(--gray-color);
    text-decoration: none;
    transition: color var(--transition-fast);
}

.footer-section ul li a:hover {
    color: var(--secondary-color);
}

.footer-contact p {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.5rem;
    color: var(--gray-color);
}

.footer-contact i {
    color: var(--primary-color);
    min-width: 20px;
}

.footer-bottom {
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    padding-top: 2rem;
    text-align: center;
    color: var(--gray-color);
}

/* Responsive Design */
@media (max-width: 768px) {
    .nav-menu {
        position: fixed;
        top: 70px;
        left: -100%;
        width: 100%;
        height: calc(100vh - 70px);
        background: var(--white-color);
        flex-direction: column;
        align-items: center;
        justify-content: flex-start;
        padding-top: 2rem;
        transition: left var(--transition-normal);
        box-shadow: var(--shadow-lg);
    }

    .nav-menu.active {
        left: 0;
    }

    .nav-toggle {
        display: flex;
    }

    .nav-toggle.active .bar:nth-child(2) {
        opacity: 0;
    }

    .nav-toggle.active .bar:nth-child(1) {
        transform: translateY(8px) rotate(45deg);
    }

    .nav-toggle.active .bar:nth-child(3) {
        transform: translateY(-8px) rotate(-45deg);
    }

    .logo-m {
        width: 40px;
        height: 40px;
        font-size: 1.6rem;
        border-radius: 10px;
    }

    .logo-text {
        font-size: 1.3rem;
    }

    .footer-logo-m {
        width: 50px !important;
        height: 50px !important;
        font-size: 1.8rem !important;
        border-radius: 12px !important;
    }

    .hero-content {
        grid-template-columns: 1fr;
        text-align: center;
        gap: 2rem;
    }

    .title-main {
        font-size: 2.5rem;
    }

    .hero-buttons {
        justify-content: center;
        flex-wrap: wrap;
    }

    .about-content,
    .contact-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .about-stats {
        grid-template-columns: 1fr;
    }

    .services-grid {
        grid-template-columns: 1fr;
    }

    .portfolio-grid {
        grid-template-columns: 1fr;
    }

    .section-title {
        font-size: 2.5rem;
    }

    .hero-avatar {
        width: 250px;
        height: 250px;
        margin: 2rem auto;
    }
    
    .avatar-ring {
        top: -12px;
        left: -12px;
        right: -12px;
        bottom: -12px;
        border-width: 3px;
    }

    .ai-chat {
        width: 300px;
        height: 400px;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 0 15px;
    }

    .nav-container {
        padding: 1rem 1.5rem;
    }

    .hero-content {
        padding: 1rem;
    }

    .title-main {
        font-size: 2rem;
    }

    .title-sub {
        font-size: 1.2rem;
    }

    .hero-description {
        font-size: 1rem;
    }

    .section-title {
        font-size: 2rem;
    }

    .hero-avatar {
        width: 200px;
        height: 200px;
        margin: 1.5rem auto;
    }
    
    .avatar-ring {
        top: -10px;
        left: -10px;
        right: -10px;
        bottom: -10px;
        border-width: 2px;
    }

    .ai-assistant {
        bottom: 1rem;
        right: 1rem;
    }

    .ai-chat {
        width: 280px;
        height: 350px;
    }

    .btn {
        padding: 0.6rem 1.2rem;
        font-size: 0.9rem;
    }
}

/* Animations */
.fade-in {
    animation: fadeIn 0.6s ease-out;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.slide-in-up {
    animation: slideInUp 0.6s ease-out;
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.scale-in {
    animation: scaleIn 0.6s ease-out;
}

@keyframes scaleIn {
    from {
        opacity: 0;
        transform: scale(0.9);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

/* Utility Classes */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.mb-1 { margin-bottom: 0.5rem; }
.mb-2 { margin-bottom: 1rem; }
.mb-3 { margin-bottom: 1.5rem; }
.mb-4 { margin-bottom: 2rem; }

.mt-1 { margin-top: 0.5rem; }
.mt-2 { margin-top: 1rem; }
.mt-3 { margin-top: 1.5rem; }
.mt-4 { margin-top: 2rem; }

.hidden { display: none; }
.visible { display: block; }

/* Print Styles */
@media print {
    .navbar,
    .ai-assistant,
    .scroll-indicator,
    .hero-buttons,
    .hero-social,
    .contact-form {
        display: none;
    }
    
    body {
        color: black;
        background: white;
    }
    
    .hero-background {
        display: none;
    }
    
    .hero-content {
        color: black;
    }
}
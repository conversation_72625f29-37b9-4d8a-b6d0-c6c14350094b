@echo off
echo ========================================
echo   Mohamed Al-Ashrafi Portfolio Deployment
echo ========================================
echo.

echo Checking files...
if not exist "index.html" (
    echo ERROR: index.html not found!
    pause
    exit /b 1
)

if not exist "styles\main.css" (
    echo ERROR: styles\main.css not found!
    pause
    exit /b 1
)

if not exist "js\main.js" (
    echo ERROR: js\main.js not found!
    pause
    exit /b 1
)

echo All required files found!
echo.

echo Starting local server for testing...
echo Open your browser and go to: http://localhost:8080
echo Press Ctrl+C to stop the server when ready to deploy
echo.

python -m http.server 8080

echo.
echo ========================================
echo   Ready for Deployment!
echo ========================================
echo.
echo Next steps:
echo 1. Go to https://netlify.com
echo 2. Sign up/Login
echo 3. Drag and drop this entire folder
echo 4. Your site will be live!
echo.
echo Alternative: Use Git deployment
echo 1. git init
echo 2. git add .
echo 3. git commit -m "Initial commit"
echo 4. Push to GitHub
echo 5. Connect GitHub to Netlify
echo.
pause

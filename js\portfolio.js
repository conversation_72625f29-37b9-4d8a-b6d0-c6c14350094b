// Portfolio Management System for <PERSON> Al-Ashrafi's Website

document.addEventListener('DOMContentLoaded', function() {
    initializePortfolio();
});

// Portfolio data
const portfolioData = [
    {
        id: 1,
        title: "هوية بصرية لشركة التقنية المتقدمة",
        category: "brand",
        icon: "fas fa-palette",
        iconColor: "#f59e0b",
        description: "تصميم هوية بصرية كاملة تشمل الشعار ودليل الهوية وتطبيقاتها المختلفة",
        tags: ["هوية بصرية", "شعار", "برanding"],
        client: "شركة التقنية المتقدمة",
        year: "2024",
        link: "#",
        featured: true
    },
    {
        id: 2,
        title: "موقع إلكتروني لمطعم الأصالة",
        category: "web",
        icon: "fas fa-utensils",
        iconColor: "#ef4444",
        description: "تصميم وتطوير موقع إلكتروني متجاوب لمطعم يبرز الأطباق والخدمات",
        tags: ["تصميم ويب", "مطاعم", "UI/UX"],
        client: "مطعم الأصالة",
        year: "2024",
        link: "#",
        featured: true
    },
    {
        id: 3,
        title: "تطبيق توصيل الطعام",
        category: "app",
        icon: "fas fa-mobile-alt",
        iconColor: "#8b5cf6",
        description: "تصميم واجهات تطبيق توصيل الطعام بتجربة مستخدم سهلة ومتميزة",
        tags: ["تطبيق", "UI/UX", "توصيل"],
        client: "شركة التوصيل السريع",
        year: "2024",
        link: "#",
        featured: false
    },
    {
        id: 4,
        title: "كتالوج منتجات إلكتروني",
        category: "print",
        icon: "fas fa-book-open",
        iconColor: "#10b981",
        description: "تصميم كتالوج منتجات رقمي وقابل للطباعة بتصميم أنيق ومعاصر",
        tags: ["كتالوج", "منتجات", "طباعة"],
        client: "شركة الإلكترونيات الحديثة",
        year: "2023",
        link: "#",
        featured: false
    },
    {
        id: 5,
        title: "هوية بصرية لعيادة طبية",
        category: "brand",
        icon: "fas fa-heartbeat",
        iconColor: "#06b6d4",
        description: "تطوير هوية بصرية شاملة لعيادة طبية تعكس الثقة والاحترافية",
        tags: ["طبي", "هوية", "احترافي"],
        client: "عيادة الدكتور أحمد",
        year: "2023",
        link: "#",
        featured: true
    },
    {
        id: 6,
        title: "موقع شركة استشارات",
        category: "web",
        icon: "fas fa-chart-line",
        iconColor: "#2563eb",
        description: "موقع إلكتروني لشركة استشارات مالية مع لوحة تحكم متقدمة",
        tags: ["استشارات", "مالية", "لوحة تحكم"],
        client: "شركة الاستشارات المالية",
        year: "2023",
        link: "#",
        featured: false
    },
    {
        id: 7,
        title: "تطبيق إدارة المهام",
        category: "app",
        icon: "fas fa-tasks",
        iconColor: "#f97316",
        description: "تصميم تطبيق لإدارة المهام والمشاريع بواجهة نظيفة وسهلة الاستخدام",
        tags: ["إنتاجية", "مهام", "إدارة"],
        client: "شركة الإنتاجية",
        year: "2023",
        link: "#",
        featured: false
    },
    {
        id: 8,
        title: "حملة إعلانية لمنتج جديد",
        category: "print",
        icon: "fas fa-bullhorn",
        iconColor: "#ec4899",
        description: "تصميم حملة إعلانية شاملة لإطلاق منتج جديد عبر القنوات المختلفة",
        tags: ["إعلان", "حملة", "منتج"],
        client: "شركة المنتجات الاستهلاكية",
        year: "2023",
        link: "#",
        featured: true
    },
    {
        id: 9,
        title: "هوية بصرية لمدرسة",
        category: "brand",
        icon: "fas fa-graduation-cap",
        iconColor: "#84cc16",
        description: "تطوير هوية بصرية متكاملة لمدرسة خاصة تعكس قيم التعليم والتميز",
        tags: ["تعليم", "مدرسة", "أطفال"],
        client: "مدرسة المستقبل",
        year: "2023",
        link: "#",
        featured: false
    },
    {
        id: 10,
        title: "متجر إلكتروني للأزياء",
        category: "web",
        icon: "fas fa-shopping-bag",
        iconColor: "#f472b6",
        description: "تصميم متجر إلكتروني للأزياء مع نظام دفع متكامل وتجربة تسوق مميزة",
        tags: ["متجر", "أزياء", "تجارة إلكترونية"],
        client: "متجر الأناقة",
        year: "2023",
        link: "#",
        featured: true
    },
    {
        id: 11,
        title: "تطبيق حجز المواعيد",
        category: "app",
        icon: "fas fa-calendar-check",
        iconColor: "#06b6d4",
        description: "تطبيق لحجز المواعيد في المراكز الطبية والخدمية",
        tags: ["حجز", "مواعيد", "خدمات"],
        client: "مركز الخدمات الطبية",
        year: "2022",
        link: "#",
        featured: false
    },
    {
        id: 12,
        title: "مجلة إلكترونية",
        category: "print",
        icon: "fas fa-newspaper",
        iconColor: "#64748b",
        description: "تصميم مجلة إلكترونية شهرية بتخطيط جذاب وسهل القراءة",
        tags: ["مجلة", "إلكترونية", "نشر"],
        client: "دار النشر الحديثة",
        year: "2022",
        link: "#",
        featured: false
    }
];

let currentFilter = 'all';
let itemsPerPage = 6;
let currentPage = 1;
let filteredData = [...portfolioData];

function initializePortfolio() {
    setupFilters();
    renderPortfolio();
    setupLoadMore();
    setupPortfolioModal();
}

function setupFilters() {
    const filterButtons = document.querySelectorAll('.filter-btn');
    
    filterButtons.forEach(button => {
        button.addEventListener('click', function() {
            // Update active filter
            filterButtons.forEach(btn => btn.classList.remove('active'));
            this.classList.add('active');
            
            // Get filter value
            currentFilter = this.dataset.filter;
            currentPage = 1;
            
            // Filter data
            if (currentFilter === 'all') {
                filteredData = [...portfolioData];
            } else {
                filteredData = portfolioData.filter(item => item.category === currentFilter);
            }
            
            // Re-render portfolio
            renderPortfolio();
        });
    });
}

function renderPortfolio() {
    const portfolioGrid = document.getElementById('portfolio-grid');
    const startIndex = 0;
    const endIndex = currentPage * itemsPerPage;
    const itemsToShow = filteredData.slice(startIndex, endIndex);
    
    // Clear existing items if it's a new filter
    if (currentPage === 1) {
        portfolioGrid.innerHTML = '';
    }
    
    // Create portfolio items
    itemsToShow.forEach((item, index) => {
        if (index >= (currentPage - 1) * itemsPerPage) {
            const portfolioItem = createPortfolioItem(item);
            portfolioGrid.appendChild(portfolioItem);
        }
    });
    
    // Update load more button
    updateLoadMoreButton();
    
    // Animate new items
    animateNewItems();
}

function createPortfolioItem(item) {
    const portfolioItem = document.createElement('div');
    portfolioItem.className = `portfolio-item ${item.category}`;
    portfolioItem.setAttribute('data-category', item.category);
    
    portfolioItem.innerHTML = `
        <div class="portfolio-icon-container">
            <div class="portfolio-icon" style="color: ${item.iconColor}">
                <i class="${item.icon}"></i>
            </div>
            <div class="portfolio-overlay">
                <div class="portfolio-links">
                    <a href="#" class="portfolio-link view-link" data-id="${item.id}">
                        <i class="fas fa-eye"></i>
                    </a>
                    <a href="${item.link}" class="portfolio-link external-link" target="_blank">
                        <i class="fas fa-external-link-alt"></i>
                    </a>
                </div>
            </div>
        </div>
        <div class="portfolio-content">
            <div class="portfolio-category">${getCategoryName(item.category)}</div>
            <h3 class="portfolio-title">${item.title}</h3>
            <p class="portfolio-description">${item.description}</p>
            <div class="portfolio-tags">
                ${item.tags.map(tag => `<span class="portfolio-tag">${tag}</span>`).join('')}
            </div>
            <div class="portfolio-meta">
                <span class="portfolio-client">العميل: ${item.client}</span>
                <span class="portfolio-year">السنة: ${item.year}</span>
            </div>
        </div>
    `;
    
    return portfolioItem;
}

function getCategoryName(category) {
    const categoryNames = {
        'web': 'مواقع إلكترونية',
        'brand': 'هوية بصرية',
        'app': 'تطبيقات',
        'print': 'مطبوعات'
    };
    return categoryNames[category] || category;
}

function setupLoadMore() {
    const loadMoreBtn = document.getElementById('load-more-btn');
    
    loadMoreBtn.addEventListener('click', function() {
        currentPage++;
        renderPortfolio();
    });
}

function updateLoadMoreButton() {
    const loadMoreBtn = document.getElementById('load-more-btn');
    const totalItems = filteredData.length;
    const shownItems = currentPage * itemsPerPage;
    
    if (shownItems >= totalItems) {
        loadMoreBtn.style.display = 'none';
    } else {
        loadMoreBtn.style.display = 'inline-flex';
        loadMoreBtn.innerHTML = `
            <i class="fas fa-plus"></i>
            عرض المزيد (${Math.min(itemsPerPage, totalItems - shownItems)} عنصر)
        `;
    }
}

function animateNewItems() {
    const newItems = document.querySelectorAll('.portfolio-item:not(.animated)');
    
    newItems.forEach((item, index) => {
        item.classList.add('animated');
        item.style.opacity = '0';
        item.style.transform = 'translateY(30px)';
        
        setTimeout(() => {
            item.style.transition = 'all 0.6s ease-out';
            item.style.opacity = '1';
            item.style.transform = 'translateY(0)';
        }, index * 100);
    });
}

function setupPortfolioModal() {
    // Create modal HTML
    const modal = document.createElement('div');
    modal.className = 'portfolio-modal';
    modal.innerHTML = `
        <div class="modal-backdrop">
            <div class="modal-content">
                <button class="modal-close">
                    <i class="fas fa-times"></i>
                </button>
                <div class="modal-body">
                    <div class="modal-image">
                        <img src="" alt="" id="modal-image">
                    </div>
                    <div class="modal-details">
                        <div class="modal-category" id="modal-category"></div>
                        <h2 class="modal-title" id="modal-title"></h2>
                        <p class="modal-description" id="modal-description"></p>
                        <div class="modal-meta">
                            <div class="meta-item">
                                <strong>العميل:</strong>
                                <span id="modal-client"></span>
                            </div>
                            <div class="meta-item">
                                <strong>السنة:</strong>
                                <span id="modal-year"></span>
                            </div>
                            <div class="meta-item">
                                <strong>الفئة:</strong>
                                <span id="modal-category-text"></span>
                            </div>
                        </div>
                        <div class="modal-tags" id="modal-tags"></div>
                        <div class="modal-actions">
                            <a href="#" class="btn btn-primary" id="modal-link" target="_blank">
                                <i class="fas fa-external-link-alt"></i>
                                عرض المشروع
                            </a>
                            <button class="btn btn-secondary" id="modal-contact">
                                <i class="fas fa-phone"></i>
                                طلب مشروع مماثل
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    document.body.appendChild(modal);
    
    // Add modal styles
    const modalStyles = `
        .portfolio-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 9999;
            display: none;
            align-items: center;
            justify-content: center;
        }
        
        .modal-backdrop {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            backdrop-filter: blur(5px);
        }
        
        .modal-content {
            position: relative;
            background: white;
            border-radius: 12px;
            max-width: 900px;
            max-height: 90vh;
            width: 90%;
            overflow: hidden;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
        }
        
        .modal-close {
            position: absolute;
            top: 1rem;
            right: 1rem;
            background: rgba(255, 255, 255, 0.9);
            border: none;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            cursor: pointer;
            z-index: 10;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
            transition: all 0.3s ease;
        }
        
        .modal-close:hover {
            background: white;
            transform: scale(1.1);
        }
        
        .modal-body {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 0;
            max-height: 90vh;
            overflow: auto;
        }
        
        .modal-image {
            background: #f8f9fa;
            display: flex;
            align-items: center;
            justify-content: center;
            min-height: 400px;
        }
        
        .modal-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        
        .modal-details {
            padding: 2rem;
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }
        
        .modal-category {
            color: var(--primary-color);
            font-weight: 600;
            font-size: 0.9rem;
            text-transform: uppercase;
        }
        
        .modal-title {
            font-size: 1.8rem;
            font-weight: 700;
            color: var(--dark-color);
            margin: 0;
        }
        
        .modal-description {
            color: var(--gray-color);
            line-height: 1.6;
            margin: 0;
        }
        
        .modal-meta {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
            padding: 1rem;
            background: var(--light-color);
            border-radius: 8px;
        }
        
        .meta-item {
            display: flex;
            justify-content: space-between;
        }
        
        .modal-tags {
            display: flex;
            gap: 0.5rem;
            flex-wrap: wrap;
        }
        
        .modal-actions {
            display: flex;
            gap: 1rem;
            margin-top: auto;
        }
        
        @media (max-width: 768px) {
            .modal-body {
                grid-template-columns: 1fr;
            }
            
            .modal-actions {
                flex-direction: column;
            }
        }
    `;
    
    const styleSheet = document.createElement('style');
    styleSheet.textContent = modalStyles;
    document.head.appendChild(styleSheet);
    
    // Event listeners
    const modalBackdrop = modal.querySelector('.modal-backdrop');
    const modalClose = modal.querySelector('.modal-close');
    
    modalBackdrop.addEventListener('click', function(e) {
        if (e.target === modalBackdrop) {
            closeModal();
        }
    });
    
    modalClose.addEventListener('click', closeModal);
    
    // Handle view links
    document.addEventListener('click', function(e) {
        if (e.target.closest('.view-link')) {
            e.preventDefault();
            const id = parseInt(e.target.closest('.view-link').dataset.id);
            openModal(id);
        }
    });
    
    function openModal(id) {
        const item = portfolioData.find(p => p.id === id);
        if (!item) return;
        
        // Populate modal content
        document.getElementById('modal-image').src = item.image;
        document.getElementById('modal-image').alt = item.title;
        document.getElementById('modal-category').textContent = getCategoryName(item.category);
        document.getElementById('modal-title').textContent = item.title;
        document.getElementById('modal-description').textContent = item.description;
        document.getElementById('modal-client').textContent = item.client;
        document.getElementById('modal-year').textContent = item.year;
        document.getElementById('modal-category-text').textContent = getCategoryName(item.category);
        document.getElementById('modal-link').href = item.link;
        
        // Tags
        const tagsContainer = document.getElementById('modal-tags');
        tagsContainer.innerHTML = item.tags.map(tag => `<span class="portfolio-tag">${tag}</span>`).join('');
        
        // Contact button
        const contactBtn = document.getElementById('modal-contact');
        contactBtn.addEventListener('click', function() {
            const message = `مرحباً محمد، أعجبني مشروع "${item.title}" وأريد عمل مشروع مماثل. يرجى التواصل معي.`;
            const whatsappUrl = `https://wa.me/01014840269?text=${encodeURIComponent(message)}`;
            window.open(whatsappUrl, '_blank');
        });
        
        // Show modal
        modal.style.display = 'flex';
        document.body.style.overflow = 'hidden';
        
        // Animate in
        setTimeout(() => {
            modal.style.opacity = '1';
            modal.querySelector('.modal-content').style.transform = 'scale(1)';
        }, 10);
    }
    
    function closeModal() {
        modal.style.opacity = '0';
        modal.querySelector('.modal-content').style.transform = 'scale(0.95)';
        
        setTimeout(() => {
            modal.style.display = 'none';
            document.body.style.overflow = 'auto';
        }, 300);
    }
}

// Search functionality
function addPortfolioSearch() {
    const searchContainer = document.querySelector('.portfolio-filters');
    const searchInput = document.createElement('input');
    searchInput.type = 'text';
    searchInput.placeholder = 'ابحث في الأعمال...';
    searchInput.className = 'portfolio-search';
    searchInput.style.cssText = `
        padding: 0.75rem 1rem;
        border: 2px solid var(--light-color);
        border-radius: var(--border-radius);
        font-size: 1rem;
        width: 250px;
        margin-bottom: 1rem;
        transition: border-color 0.3s ease;
    `;
    
    searchContainer.appendChild(searchInput);
    
    searchInput.addEventListener('input', function(e) {
        const searchTerm = e.target.value.toLowerCase();
        
        if (currentFilter === 'all') {
            filteredData = portfolioData.filter(item => 
                item.title.toLowerCase().includes(searchTerm) ||
                item.description.toLowerCase().includes(searchTerm) ||
                item.tags.some(tag => tag.toLowerCase().includes(searchTerm)) ||
                item.client.toLowerCase().includes(searchTerm)
            );
        } else {
            filteredData = portfolioData.filter(item => 
                item.category === currentFilter &&
                (item.title.toLowerCase().includes(searchTerm) ||
                item.description.toLowerCase().includes(searchTerm) ||
                item.tags.some(tag => tag.toLowerCase().includes(searchTerm)) ||
                item.client.toLowerCase().includes(searchTerm))
            );
        }
        
        currentPage = 1;
        renderPortfolio();
    });
}

// Initialize search after DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    setTimeout(addPortfolioSearch, 100);
});

// Export functions for external use
window.portfolioManager = {
    addProject: function(project) {
        portfolioData.unshift({
            ...project,
            id: Date.now()
        });
        if (currentFilter === 'all' || currentFilter === project.category) {
            filteredData = currentFilter === 'all' ? [...portfolioData] : portfolioData.filter(item => item.category === currentFilter);
            renderPortfolio();
        }
    },
    
    removeProject: function(id) {
        const index = portfolioData.findIndex(p => p.id === id);
        if (index > -1) {
            portfolioData.splice(index, 1);
            filteredData = currentFilter === 'all' ? [...portfolioData] : portfolioData.filter(item => item.category === currentFilter);
            renderPortfolio();
        }
    },
    
    getProjects: function() {
        return [...portfolioData];
    }
};
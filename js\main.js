// Main JavaScript file for <PERSON>'s website

document.addEventListener('DOMContentLoaded', function() {
    // Initialize all components
    initializeNavigation();
    initializeHero();
    initializeAnimations();
    initializeStats();
    initializeSkills();
    initializePortfolio();
    initializeReviews();
    initializeContactForm();
    initializeAIAssistant();
    initializeScrollEffects();
});

// Navigation
function initializeNavigation() {
    const navbar = document.getElementById('navbar');
    const navToggle = document.getElementById('nav-toggle');
    const navMenu = document.getElementById('nav-menu');
    const navLinks = document.querySelectorAll('.nav-link');

    // Mobile menu toggle
    navToggle.addEventListener('click', function() {
        navMenu.classList.toggle('active');
        navToggle.classList.toggle('active');
    });

    // Close mobile menu when clicking on links
    navLinks.forEach(link => {
        link.addEventListener('click', function() {
            navMenu.classList.remove('active');
            navToggle.classList.remove('active');
            
            // Update active link
            navLinks.forEach(l => l.classList.remove('active'));
            this.classList.add('active');
        });
    });

    // Navbar scroll effect
    window.addEventListener('scroll', function() {
        if (window.scrollY > 100) {
            navbar.classList.add('scrolled');
        } else {
            navbar.classList.remove('scrolled');
        }
    });

    // Active section highlighting
    window.addEventListener('scroll', updateActiveSection);
}

function updateActiveSection() {
    const sections = document.querySelectorAll('section[id]');
    const navLinks = document.querySelectorAll('.nav-link');
    
    let current = '';
    
    sections.forEach(section => {
        const sectionTop = section.offsetTop;

        if (window.pageYOffset >= sectionTop - 200) {
            current = section.getAttribute('id');
        }
    });
    
    navLinks.forEach(link => {
        link.classList.remove('active');
        if (link.getAttribute('href') === '#' + current) {
            link.classList.add('active');
        }
    });
}

// Hero Section
function initializeHero() {
    const heroText = document.querySelector('.hero-text');
    const heroImage = document.querySelector('.hero-image');

    // Ensure elements are visible immediately
    if (heroText) {
        heroText.style.opacity = '1';
        heroText.style.transform = 'translateY(0)';
    }

    if (heroImage) {
        heroImage.style.opacity = '1';
        heroImage.style.transform = 'translateX(0)';
    }
}

// Scroll Animations
function initializeAnimations() {
    // Simplified animation system that doesn't hide critical elements
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('fade-in');
                // Don't unobserve to allow re-animation if needed
            }
        });
    }, observerOptions);

    // Only animate non-critical elements
    setTimeout(() => {
        const elementsToAnimate = document.querySelectorAll('.skill-item, .portfolio-item:not(.featured)');
        elementsToAnimate.forEach((el) => {
            observer.observe(el);
        });

        // Ensure all critical elements are always visible
        const criticalElements = document.querySelectorAll('.service-card, .contact-item, .hero-text, .hero-image');
        criticalElements.forEach(el => {
            el.style.opacity = '1';
            el.style.visibility = 'visible';
            el.style.transform = 'none';
        });
    }, 100);
}

// Animated Statistics
function initializeStats() {
    const statNumbers = document.querySelectorAll('.stat-number');
    
    const statsObserver = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const target = parseInt(entry.target.dataset.target);
                animateNumber(entry.target, 0, target, 2000);
                statsObserver.unobserve(entry.target);
            }
        });
    });
    
    statNumbers.forEach(stat => {
        statsObserver.observe(stat);
    });
}

function animateNumber(element, start, end, duration) {
    const range = end - start;
    const increment = range / (duration / 16);
    let current = start;
    
    const timer = setInterval(() => {
        current += increment;
        if (current >= end) {
            current = end;
            clearInterval(timer);
        }
        element.textContent = Math.floor(current);
    }, 16);
}

// Skills Animation
function initializeSkills() {
    const skillBars = document.querySelectorAll('.skill-progress');
    
    const skillsObserver = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const width = entry.target.dataset.width;
                setTimeout(() => {
                    entry.target.style.width = width;
                }, 500);
                skillsObserver.unobserve(entry.target);
            }
        });
    });
    
    skillBars.forEach(bar => {
        skillsObserver.observe(bar);
    });
}

// Portfolio Interactive Gallery
function initializePortfolio() {
    const filterBtns = document.querySelectorAll('.filter-btn');
    const portfolioItems = document.querySelectorAll('.portfolio-item');

    // Filter functionality
    filterBtns.forEach(btn => {
        btn.addEventListener('click', () => {
            const filter = btn.getAttribute('data-filter');

            // Update active button
            filterBtns.forEach(b => b.classList.remove('active'));
            btn.classList.add('active');

            // Filter items
            portfolioItems.forEach(item => {
                const category = item.getAttribute('data-category');

                if (filter === 'all' || category === filter) {
                    item.style.display = 'block';
                    setTimeout(() => {
                        item.style.opacity = '1';
                        item.style.transform = 'translateY(0)';
                    }, 100);
                } else {
                    item.style.opacity = '0';
                    item.style.transform = 'translateY(20px)';
                    setTimeout(() => {
                        item.style.display = 'none';
                    }, 300);
                }
            });
        });
    });

    // Load more functionality
    const loadMoreBtn = document.getElementById('load-more-btn');
    if (loadMoreBtn) {
        loadMoreBtn.addEventListener('click', () => {
            // Simulate loading more items
            showNotification('تم تحميل المزيد من المشاريع!', 'success');
        });
    }
}

// Portfolio Modal Functions
function openPortfolioModal(projectId) {
    const modal = document.getElementById('portfolio-modal');
    const projectData = getProjectData(projectId);

    if (!projectData) return;

    // Populate modal content
    document.getElementById('modal-title').textContent = projectData.title;
    document.getElementById('modal-image').src = projectData.image;
    document.getElementById('modal-image').alt = projectData.title;
    document.getElementById('modal-description').textContent = projectData.description;
    document.getElementById('modal-client').textContent = projectData.client;
    document.getElementById('modal-date').textContent = projectData.date;
    document.getElementById('modal-category').textContent = projectData.category;

    // Populate tags
    const tagsContainer = document.getElementById('modal-tags');
    tagsContainer.innerHTML = '';
    projectData.tags.forEach(tag => {
        const tagElement = document.createElement('span');
        tagElement.className = 'tag';
        tagElement.textContent = tag;
        tagsContainer.appendChild(tagElement);
    });

    // Set link
    const modalLink = document.getElementById('modal-link');
    if (projectData.link) {
        modalLink.href = projectData.link;
        modalLink.style.display = 'inline-flex';
    } else {
        modalLink.style.display = 'none';
    }

    // Show modal
    modal.classList.add('active');
    document.body.style.overflow = 'hidden';
}

function closePortfolioModal() {
    const modal = document.getElementById('portfolio-modal');
    modal.classList.remove('active');
    document.body.style.overflow = 'auto';
}

function getProjectData(projectId) {
    const projects = {
        'web1': {
            title: 'موقع شركة التقنية الحديثة',
            image: 'assets/portfolio/web1.jpg',
            description: 'تصميم وتطوير موقع إلكتروني متجاوب لشركة تقنية رائدة مع نظام إدارة محتوى متقدم وواجهة مستخدم عصرية.',
            client: 'شركة التقنية الحديثة',
            date: '2023',
            category: 'تصميم مواقع',
            tags: ['تصميم UI/UX', 'تطوير ويب', 'نظام إدارة المحتوى'],
            link: '#'
        },
        'brand1': {
            title: 'هوية بصرية لمطعم الأصالة',
            image: 'assets/portfolio/brand1.jpg',
            description: 'تصميم هوية بصرية شاملة تعكس التراث والأصالة العربية مع شعار مميز ونظام ألوان متناسق.',
            client: 'مطعم الأصالة',
            date: '2023',
            category: 'هوية بصرية',
            tags: ['تصميم شعار', 'هوية بصرية', 'تراث عربي'],
            link: null
        },
        'app1': {
            title: 'تطبيق التسوق الذكي',
            image: 'assets/portfolio/app1.jpg',
            description: 'تصميم واجهة مستخدم لتطبيق تسوق إلكتروني متطور مع تجربة مستخدم سلسة ونظام دفع آمن.',
            client: 'شركة التسوق الذكي',
            date: '2023',
            category: 'تصميم تطبيقات',
            tags: ['تصميم تطبيقات', 'UX Design', 'تجارة إلكترونية'],
            link: null
        },
        'web2': {
            title: 'منصة التعليم الإلكتروني',
            image: 'assets/portfolio/web2.jpg',
            description: 'تصميم منصة تعليمية تفاعلية مع نظام إدارة المحتوى التعليمي ونظام تتبع تقدم الطلاب.',
            client: 'أكاديمية المستقبل',
            date: '2023',
            category: 'تصميم مواقع',
            tags: ['تصميم ويب', 'نظام إدارة', 'تعليم إلكتروني'],
            link: '#'
        },
        'print1': {
            title: 'كتالوج منتجات شركة الإبداع',
            image: 'assets/portfolio/print1.jpg',
            description: 'تصميم كتالوج منتجات احترافي بتخطيط جذاب ومعلومات واضحة مع صور عالية الجودة.',
            client: 'شركة الإبداع',
            date: '2023',
            category: 'تصميم مطبوعات',
            tags: ['تصميم مطبوعات', 'كتالوج', 'تصوير منتجات'],
            link: null
        },
        'brand2': {
            title: 'هوية بصرية لعيادة طبية',
            image: 'assets/portfolio/brand2.jpg',
            description: 'تصميم هوية بصرية طبية تعكس الثقة والاحترافية مع ألوان هادئة ورموز طبية مناسبة.',
            client: 'عيادة الشفاء الطبية',
            date: '2023',
            category: 'هوية بصرية',
            tags: ['هوية طبية', 'تصميم شعار', 'ألوان هادئة'],
            link: null
        }
    };

    return projects[projectId] || null;
}

// Close modal when clicking outside
document.addEventListener('click', (e) => {
    const modal = document.getElementById('portfolio-modal');
    if (e.target === modal) {
        closePortfolioModal();
    }
});

// Close modal with Escape key
document.addEventListener('keydown', (e) => {
    if (e.key === 'Escape') {
        closePortfolioModal();
    }
});

// Reviews Slider
function initializeReviews() {
    const reviewCards = document.querySelectorAll('.review-card');
    const indicators = document.querySelectorAll('.indicator');
    const prevBtn = document.getElementById('prev-review');
    const nextBtn = document.getElementById('next-review');

    if (!reviewCards.length) return;

    let currentReview = 0;
    const totalReviews = reviewCards.length;

    function showReview(index) {
        // Hide all reviews
        reviewCards.forEach((card, i) => {
            card.classList.remove('active');
            if (indicators[i]) {
                indicators[i].classList.remove('active');
            }
        });

        // Show current review
        if (reviewCards[index]) {
            reviewCards[index].classList.add('active');
        }
        if (indicators[index]) {
            indicators[index].classList.add('active');
        }
    }

    function nextReview() {
        currentReview = (currentReview + 1) % totalReviews;
        showReview(currentReview);
    }

    function prevReview() {
        currentReview = (currentReview - 1 + totalReviews) % totalReviews;
        showReview(currentReview);
    }

    // Event listeners
    if (nextBtn) {
        nextBtn.addEventListener('click', nextReview);
    }

    if (prevBtn) {
        prevBtn.addEventListener('click', prevReview);
    }

    // Indicator clicks
    indicators.forEach((indicator, index) => {
        indicator.addEventListener('click', () => {
            currentReview = index;
            showReview(currentReview);
        });
    });

    // Auto-slide every 5 seconds
    setInterval(nextReview, 5000);

    // Initialize first review
    showReview(0);
}

// Contact Form
function initializeContactForm() {
    const contactForm = document.getElementById('contact-form');

    if (!contactForm) return;

    // Add real-time validation
    const inputs = contactForm.querySelectorAll('input, select, textarea');
    inputs.forEach(input => {
        input.addEventListener('blur', validateField);
        input.addEventListener('input', clearFieldError);
    });

    contactForm.addEventListener('submit', function(e) {
        e.preventDefault();

        // Validate all fields
        if (!validateForm()) {
            showNotification('يرجى تصحيح الأخطاء في النموذج', 'error');
            return;
        }

        const formData = new FormData(contactForm);
        const data = Object.fromEntries(formData.entries());

        // Show loading state
        const submitBtn = contactForm.querySelector('button[type="submit"]');
        const originalText = submitBtn.innerHTML;
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري الإرسال...';
        submitBtn.disabled = true;
        
        // Simulate form submission
        setTimeout(() => {
            // Send to WhatsApp
            sendToWhatsApp(data);
            
            // Send to Email
            sendToEmail(data);
            
            // Save to database
            saveToDatabase(data);
            
            // Show success message
            showNotification('تم إرسال رسالتك بنجاح! سأتواصل معك قريباً.', 'success');
            
            // Reset form
            contactForm.reset();
            
            // Reset button
            submitBtn.innerHTML = originalText;
            submitBtn.disabled = false;
        }, 2000);
    });
}

function sendToWhatsApp(data) {
    const message = `مرحباً محمد، لدي استفسار من الموقع:

📧 الاسم: ${data.name}
📧 الإيميل: ${data.email}
📞 الهاتف: ${data.phone || 'غير محدد'}
🎯 الخدمة: ${getServiceName(data.service)}
💰 الميزانية: ${getBudgetName(data.budget)}
⏰ الإطار الزمني: ${getTimelineName(data.timeline)}
💬 الرسالة: ${data.message}

تم الإرسال من: موقع محمد الأشرافي`;

    const whatsappUrl = `https://wa.me/01014840269?text=${encodeURIComponent(message)}`;
    window.open(whatsappUrl, '_blank');
}

function sendToEmail(data) {
    const subject = `استفسار جديد من ${data.name} - موقع محمد الأشرافي`;
    const body = `مرحباً محمد،

لديك رسالة جديدة من موقعك الإلكتروني:

الاسم: ${data.name}
البريد الإلكتروني: ${data.email}
رقم الهاتف: ${data.phone || 'غير محدد'}
الخدمة المطلوبة: ${getServiceName(data.service)}
الميزانية المتوقعة: ${getBudgetName(data.budget)}
الإطار الزمني: ${getTimelineName(data.timeline)}

الرسالة:
${data.message}

---
تم الإرسال تلقائياً من موقع محمد الأشرافي`;

    const mailtoUrl = `mailto:<EMAIL>?subject=${encodeURIComponent(subject)}&body=${encodeURIComponent(body)}`;
    window.open(mailtoUrl);
}

function getServiceName(serviceValue) {
    const services = {
        'branding': 'تصميم الهوية البصرية',
        'web': 'تصميم المواقع الإلكترونية',
        'app': 'تصميم التطبيقات',
        'marketing': 'التسويق الرقمي',
        'photography': 'التصوير والمونتاج',
        'print': 'التصميم الإعلاني',
        'other': 'أخرى'
    };
    return services[serviceValue] || 'غير محدد';
}

function getBudgetName(budgetValue) {
    const budgets = {
        '500-1000': '500 - 1000 ريال',
        '1000-2500': '1000 - 2500 ريال',
        '2500-5000': '2500 - 5000 ريال',
        '5000-10000': '5000 - 10000 ريال',
        '10000+': 'أكثر من 10000 ريال',
        'discuss': 'للمناقشة'
    };
    return budgets[budgetValue] || 'غير محدد';
}

function getTimelineName(timelineValue) {
    const timelines = {
        'urgent': 'عاجل (أقل من أسبوع)',
        'week': 'خلال أسبوع',
        'month': 'خلال شهر',
        'flexible': 'مرن'
    };
    return timelines[timelineValue] || 'غير محدد';
}

// Form Validation Functions
function validateForm() {
    const form = document.getElementById('contact-form');
    const inputs = form.querySelectorAll('input[required], select[required], textarea[required]');
    let isValid = true;

    inputs.forEach(input => {
        if (!validateField({ target: input })) {
            isValid = false;
        }
    });

    return isValid;
}

function validateField(event) {
    const field = event.target;
    const value = field.value.trim();
    const fieldName = field.name;
    let isValid = true;
    let errorMessage = '';

    // Clear previous errors
    clearFieldError(event);

    // Required field validation
    if (field.hasAttribute('required') && !value) {
        errorMessage = 'هذا الحقل مطلوب';
        isValid = false;
    }

    // Email validation
    else if (fieldName === 'email' && value) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(value)) {
            errorMessage = 'يرجى إدخال بريد إلكتروني صحيح';
            isValid = false;
        }
    }

    // Phone validation
    else if (fieldName === 'phone' && value) {
        const phoneRegex = /^[\+]?[0-9\s\-\(\)]{10,}$/;
        if (!phoneRegex.test(value)) {
            errorMessage = 'يرجى إدخال رقم هاتف صحيح';
            isValid = false;
        }
    }

    // Name validation
    else if (fieldName === 'name' && value) {
        if (value.length < 2) {
            errorMessage = 'الاسم يجب أن يكون أكثر من حرفين';
            isValid = false;
        }
    }

    // Message validation
    else if (fieldName === 'message' && value) {
        if (value.length < 10) {
            errorMessage = 'الرسالة يجب أن تكون أكثر من 10 أحرف';
            isValid = false;
        }
    }

    if (!isValid) {
        showFieldError(field, errorMessage);
    }

    return isValid;
}

function showFieldError(field, message) {
    field.classList.add('error');

    // Remove existing error message
    const existingError = field.parentNode.querySelector('.error-message');
    if (existingError) {
        existingError.remove();
    }

    // Add new error message
    const errorDiv = document.createElement('div');
    errorDiv.className = 'error-message';
    errorDiv.textContent = message;
    field.parentNode.appendChild(errorDiv);
}

function clearFieldError(event) {
    const field = event.target;
    field.classList.remove('error');

    const errorMessage = field.parentNode.querySelector('.error-message');
    if (errorMessage) {
        errorMessage.remove();
    }
}

// Database Operations (Using localStorage for demo)
function saveToDatabase(data) {
    // Get existing messages
    const messages = JSON.parse(localStorage.getItem('contact_messages') || '[]');
    
    // Add new message
    const newMessage = {
        id: Date.now(),
        timestamp: new Date().toISOString(),
        ...data,
        status: 'new'
    };
    
    messages.unshift(newMessage);
    
    // Save back to localStorage
    localStorage.setItem('contact_messages', JSON.stringify(messages));
    
    // Send notification to admin (WhatsApp)
    sendAdminNotification(newMessage);
}

function sendAdminNotification(message) {
    const adminMessage = `🔔 رسالة جديدة من الموقع!

👤 من: ${message.name}
📧 الإيميل: ${message.email}
🎯 الخدمة: ${getServiceName(message.service)}

انقر للاطلاع على التفاصيل الكاملة.`;
    
    const whatsappUrl = `https://wa.me/01014840269?text=${encodeURIComponent(adminMessage)}`;
    
    // Send notification after a short delay
    setTimeout(() => {
        if (confirm('هل تريد إرسال إشعار لك على الواتساب؟')) {
            window.open(whatsappUrl, '_blank');
        }
    }, 1000);
}

// AI Assistant
function initializeAIAssistant() {
    const aiToggle = document.getElementById('ai-toggle');
    const aiChat = document.getElementById('ai-chat');
    const aiClose = document.getElementById('ai-close');
    const aiInputField = document.getElementById('ai-input-field');
    const aiSend = document.getElementById('ai-send');
    const aiMessages = document.getElementById('ai-messages');
    
    // Toggle AI chat
    aiToggle.addEventListener('click', function() {
        aiChat.classList.toggle('active');
    });
    
    // Close AI chat
    aiClose.addEventListener('click', function() {
        aiChat.classList.remove('active');
    });
    
    // Send message
    aiSend.addEventListener('click', sendAIMessage);
    aiInputField.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            sendAIMessage();
        }
    });
    
    function sendAIMessage() {
        const message = aiInputField.value.trim();
        if (!message) return;
        
        // Add user message
        addAIMessage(message, 'user');
        aiInputField.value = '';
        
        // Simulate AI thinking
        setTimeout(() => {
            const response = getAIResponse(message);
            addAIMessage(response, 'bot');
            
            // Send to WhatsApp if it's a contact request
            if (message.includes('تواصل') || message.includes('اتصال') || message.includes('واتساب')) {
                setTimeout(() => {
                    sendAINotificationToWhatsApp(message);
                }, 1000);
            }
        }, 1000);
    }
    
    function addAIMessage(content, sender) {
        const messageDiv = document.createElement('div');
        messageDiv.className = `ai-message ${sender}`;
        
        const messageContent = document.createElement('div');
        messageContent.className = 'message-content';
        messageContent.textContent = content;
        
        messageDiv.appendChild(messageContent);
        aiMessages.appendChild(messageDiv);
        
        // Scroll to bottom
        aiMessages.scrollTop = aiMessages.scrollHeight;
    }
    
    function getAIResponse(message) {
        const responses = {
            'مرحبا': 'مرحباً بك! أنا المساعد الذكي لمحمد الأشرافي. كيف يمكنني مساعدتك؟',
            'الخدمات': 'محمد يقدم خدمات متنوعة: تصميم الهوية البصرية، تصميم المواقع، تصميم التطبيقات، التسويق الرقمي، التصوير والمونتاج. أي خدمة تهمك؟',
            'الأسعار': 'الأسعار تبدأ من 200 ريال للتصميم الإعلاني وحتى 1500 ريال لتصميم المواقع. يمكنك التواصل معه للحصول على عرض سعر مخصص.',
            'تواصل': 'يمكنك التواصل مع محمد عبر الواتساب: 01014840269 أو الإيميل: <EMAIL>',
            'المدة': 'مدة تنفيذ المشاريع تختلف حسب نوع المشروع وتعقيده. عادة من 3-7 أيام للتصاميم البسيطة و2-4 أسابيع للمواقع الكاملة.',
            'الخبرة': 'محمد لديه 5 سنوات خبرة في التصميم وأنجز أكثر من 150 مشروع بنسبة رضا 98%'
        };
        
        // Find matching response
        for (let key in responses) {
            if (message.includes(key)) {
                return responses[key];
            }
        }
        
        // Default response
        return 'شكراً لك على رسالتك. للحصول على إجابة مفصلة، يُرجى التواصل مع محمد مباشرة عبر الواتساب أو الإيميل. سيسعد بالإجابة على جميع استفساراتك!';
    }
    
    function sendAINotificationToWhatsApp(userMessage) {
        const notification = `🤖 رسالة من المساعد الذكي:

👤 العميل سأل: "${userMessage}"

تم توجيهه للتواصل معك مباشرة.
الوقت: ${new Date().toLocaleString('ar-EG')}`;
        
        const whatsappUrl = `https://wa.me/01014840269?text=${encodeURIComponent(notification)}`;
        
        // Ask user if they want to notify
        if (confirm('هل تريد إشعار محمد الأشرافي بهذه المحادثة؟')) {
            window.open(whatsappUrl, '_blank');
        }
    }
}

// Scroll Effects
function initializeScrollEffects() {
    // Smooth scrolling for anchor links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });
    
    // Scroll to top button
    const scrollToTopBtn = document.createElement('button');
    scrollToTopBtn.innerHTML = '<i class="fas fa-arrow-up"></i>';
    scrollToTopBtn.className = 'scroll-to-top';
    scrollToTopBtn.style.cssText = `
        position: fixed;
        bottom: 2rem;
        left: 2rem;
        width: 50px;
        height: 50px;
        background: var(--primary-color);
        color: white;
        border: none;
        border-radius: 50%;
        cursor: pointer;
        display: none;
        align-items: center;
        justify-content: center;
        font-size: 1.2rem;
        z-index: 999;
        transition: all 0.3s ease;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    `;
    
    document.body.appendChild(scrollToTopBtn);
    
    scrollToTopBtn.addEventListener('click', function() {
        window.scrollTo({
            top: 0,
            behavior: 'smooth'
        });
    });
    
    window.addEventListener('scroll', function() {
        if (window.scrollY > 500) {
            scrollToTopBtn.style.display = 'flex';
        } else {
            scrollToTopBtn.style.display = 'none';
        }
    });
}

// Notification System
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.style.cssText = `
        position: fixed;
        top: 2rem;
        right: 2rem;
        background: ${type === 'success' ? '#10b981' : type === 'error' ? '#ef4444' : '#3b82f6'};
        color: white;
        padding: 1rem 1.5rem;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        z-index: 9999;
        transform: translateX(100%);
        transition: transform 0.3s ease;
        max-width: 300px;
        word-wrap: break-word;
    `;
    
    notification.textContent = message;
    document.body.appendChild(notification);
    
    // Animate in
    setTimeout(() => {
        notification.style.transform = 'translateX(0)';
    }, 100);
    
    // Remove after 5 seconds
    setTimeout(() => {
        notification.style.transform = 'translateX(100%)';
        setTimeout(() => {
            document.body.removeChild(notification);
        }, 300);
    }, 5000);
}

// Social Media Sharing
function shareOnSocialMedia(platform, url = window.location.href, text = 'تحقق من موقع محمد الأشرافي - مصمم محترف') {
    const encodedUrl = encodeURIComponent(url);
    const encodedText = encodeURIComponent(text);
    
    const socialUrls = {
        facebook: `https://www.facebook.com/sharer/sharer.php?u=${encodedUrl}`,
        twitter: `https://twitter.com/intent/tweet?url=${encodedUrl}&text=${encodedText}`,
        linkedin: `https://www.linkedin.com/sharing/share-offsite/?url=${encodedUrl}`,
        whatsapp: `https://wa.me/?text=${encodedText} ${encodedUrl}`
    };
    
    if (socialUrls[platform]) {
        window.open(socialUrls[platform], '_blank', 'width=600,height=400');
    }
}

// Performance Optimization
function optimizeImages() {
    const images = document.querySelectorAll('img');
    
    if ('IntersectionObserver' in window) {
        const imageObserver = new IntersectionObserver(function(entries) {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const img = entry.target;
                    if (img.dataset.src) {
                        img.src = img.dataset.src;
                        img.classList.add('loaded');
                        imageObserver.unobserve(img);
                    }
                }
            });
        });
        
        images.forEach(img => {
            if (img.dataset.src) {
                imageObserver.observe(img);
            }
        });
    }
}

// Initialize image optimization
document.addEventListener('DOMContentLoaded', optimizeImages);

// Error Handling
window.addEventListener('error', function(e) {
    console.error('JavaScript Error:', e.error);
    // You can send error reports to your analytics service here
});

// Service Worker Registration removed for compatibility

// Update notification functionality removed for simplicity
// Advanced AI Assistant for <PERSON>'s Website

class AIAssistant {
    constructor() {
        this.isActive = false;
        this.conversationHistory = [];
        this.userPreferences = this.loadUserPreferences();
        this.knowledgeBase = this.initializeKnowledgeBase();
        this.currentContext = null;
        this.awaitingInput = false;
        
        this.init();
    }
    
    init() {
        this.setupEventListeners();
        this.loadConversationHistory();
        this.initializeGreeting();
    }
    
    initializeKnowledgeBase() {
        return {
            services: {
                'branding': {
                    name: 'تصميم الهوية البصرية',
                    price: 'ابتداءً من 500 ريال',
                    duration: '5-7 أيام',
                    includes: ['تصميم الشعار', 'دليل الهوية البصرية', 'تطبيقات الهوية', 'ملفات المصدر'],
                    description: 'تصميم شعارات وهويات بصرية متكاملة تعك<PERSON> شخصية علامتك التجارية'
                },
                'web': {
                    name: 'تصميم المواقع الإلكترونية',
                    price: 'ابتداءً من 1500 ريال',
                    duration: '2-4 أسابيع',
                    includes: ['تصميم UI/UX', 'التطوير الكامل', 'التحسين للمحركات', 'التجاوب مع الأجهزة'],
                    description: 'تصميم وتطوير مواقع إلكترونية احترافية متجاوبة مع جميع الأجهزة'
                },
                'app': {
                    name: 'تصميم التطبيقات',
                    price: 'ابتداءً من 1000 ريال',
                    duration: '2-3 أسابيع',
                    includes: ['تصميم UI للتطبيقات', 'النماذج التفاعلية', 'دليل التصميم', 'اختبار تجربة المستخدم'],
                    description: 'تصميم واجهات تطبيقات الهاتف المحمول بتجربة مستخدم متميزة'
                },
                'marketing': {
                    name: 'التسويق الرقمي',
                    price: 'ابتداءً من 800 ريال',
                    duration: 'حسب الحملة',
                    includes: ['إدارة وسائل التواصل', 'الحملات الإعلانية', 'تحليل الأداء', 'المحتوى الإبداعي'],
                    description: 'إدارة الحملات التسويقية الرقمية وتحسين الحضور على الإنترنت'
                },
                'photography': {
                    name: 'التصوير والمونتاج',
                    price: 'ابتداءً من 300 ريال',
                    duration: '3-5 أيام',
                    includes: ['التصوير الفوتوغرافي', 'مونتاج الفيديوهات', 'التعديل والتحسين', 'التسليم بجودة عالية'],
                    description: 'خدمات التصوير الفوتوغرافي ومونتاج الفيديوهات الاحترافية'
                },
                'print': {
                    name: 'التصميم الإعلاني',
                    price: 'ابتداءً من 200 ريال',
                    duration: '2-3 أيام',
                    includes: ['إعلانات وسائل التواصل', 'البنرات الإعلانية', 'المواد المطبوعة', 'التصاميم التفاعلية'],
                    description: 'تصميم إعلانات ومواد تسويقية احترافية لمختلف المنصات الرقمية'
                }
            },
            
            contact: {
                whatsapp: '01014840269',
                email: '<EMAIL>',
                workingHours: 'السبت - الخميس: 9:00 - 18:00',
                location: 'مصر'
            },
            
            about: {
                name: 'محمد الأشرافي',
                title: 'مصمم محترف',
                experience: '5 سنوات',
                projects: '150+ مشروع',
                satisfaction: '98%',
                specialties: ['التصميم الجرافيكي', 'تصميم المواقع', 'الهوية البصرية', 'UI/UX']
            },
            
            faqs: [
                {
                    question: 'كم يستغرق تنفيذ المشروع',
                    answer: 'المدة تختلف حسب نوع المشروع. التصاميم البسيطة 2-3 أيام، والمواقع الكاملة 2-4 أسابيع.'
                },
                {
                    question: 'هل تقدم تعديلات مجانية',
                    answer: 'نعم، أقدم حتى 3 تعديلات مجانية على كل مشروع لضمان رضاك التام.'
                },
                {
                    question: 'ما هي طرق الدفع المتاحة',
                    answer: 'نقبل الدفع عبر التحويل البنكي، فودافون كاش، أو عند التسليم حسب طبيعة المشروع.'
                },
                {
                    question: 'هل تعمل مع العملاء خارج مصر',
                    answer: 'نعم، أعمل مع عملاء من جميع أنحاء العالم العربي والتواصل يتم أونلاين.'
                }
            ]
        };
    }
    
    setupEventListeners() {
        const aiToggle = document.getElementById('ai-toggle');
        const aiClose = document.getElementById('ai-close');
        const aiSend = document.getElementById('ai-send');
        const aiInputField = document.getElementById('ai-input-field');
        
        if (aiToggle) {
            aiToggle.addEventListener('click', () => this.toggleChat());
        }
        
        if (aiClose) {
            aiClose.addEventListener('click', () => this.closeChat());
        }
        
        if (aiSend) {
            aiSend.addEventListener('click', () => this.sendMessage());
        }
        
        if (aiInputField) {
            aiInputField.addEventListener('keypress', (e) => {
                if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    this.sendMessage();
                }
            });
            
            aiInputField.addEventListener('input', () => this.handleTyping());
        }
        
        // Handle page visibility for notifications
        document.addEventListener('visibilitychange', () => {
            if (document.hidden && this.isActive) {
                this.handlePageHidden();
            }
        });
    }
    
    toggleChat() {
        const aiChat = document.getElementById('ai-chat');
        this.isActive = !this.isActive;
        
        if (this.isActive) {
            aiChat.classList.add('active');
            this.trackEvent('ai_chat_opened');
            
            // Focus input field
            setTimeout(() => {
                document.getElementById('ai-input-field').focus();
            }, 300);
        } else {
            aiChat.classList.remove('active');
            this.trackEvent('ai_chat_closed');
        }
    }
    
    closeChat() {
        const aiChat = document.getElementById('ai-chat');
        aiChat.classList.remove('active');
        this.isActive = false;
        this.trackEvent('ai_chat_closed');
    }
    
    sendMessage() {
        const inputField = document.getElementById('ai-input-field');
        const message = inputField.value.trim();
        
        if (!message) return;
        
        // Add user message to chat
        this.addMessage(message, 'user');
        inputField.value = '';
        
        // Show typing indicator
        this.showTypingIndicator();
        
        // Process message and generate response
        setTimeout(() => {
            this.hideTypingIndicator();
            this.processMessage(message);
        }, 1000 + Math.random() * 1000); // Simulate thinking time
    }
    
    addMessage(content, sender, options = {}) {
        const messagesContainer = document.getElementById('ai-messages');
        const messageDiv = document.createElement('div');
        messageDiv.className = `ai-message ${sender}`;
        
        if (options.isHTML) {
            messageDiv.innerHTML = `<div class="message-content">${content}</div>`;
        } else {
            const messageContent = document.createElement('div');
            messageContent.className = 'message-content';
            messageContent.textContent = content;
            messageDiv.appendChild(messageContent);
        }
        
        // Add timestamp
        if (options.showTime) {
            const timestamp = document.createElement('div');
            timestamp.className = 'message-timestamp';
            timestamp.textContent = new Date().toLocaleTimeString('ar-EG', {
                hour: '2-digit',
                minute: '2-digit'
            });
            messageDiv.appendChild(timestamp);
        }
        
        messagesContainer.appendChild(messageDiv);
        
        // Scroll to bottom
        messagesContainer.scrollTop = messagesContainer.scrollHeight;
        
        // Add to conversation history
        this.conversationHistory.push({
            content,
            sender,
            timestamp: new Date().toISOString(),
            ...options
        });
        
        // Save conversation
        this.saveConversationHistory();
        
        // Animate message
        messageDiv.style.opacity = '0';
        messageDiv.style.transform = 'translateY(10px)';
        setTimeout(() => {
            messageDiv.style.transition = 'all 0.3s ease';
            messageDiv.style.opacity = '1';
            messageDiv.style.transform = 'translateY(0)';
        }, 50);
    }
    
    processMessage(message) {
        const response = this.generateResponse(message);
        
        // Add bot response
        this.addMessage(response.text, 'bot', {
            isHTML: response.isHTML,
            showTime: true
        });
        
        // Handle special actions
        if (response.actions) {
            response.actions.forEach(action => {
                setTimeout(() => this.executeAction(action), 500);
            });
        }
        
        // Send notification to admin if needed
        if (response.notifyAdmin) {
            this.notifyAdmin(message, response.text);
        }
        
        this.trackEvent('ai_message_sent', { message, response: response.text });
    }
    
    generateResponse(message) {
        const lowerMessage = message.toLowerCase();
        
        // Greeting responses
        if (this.matchesKeywords(lowerMessage, ['مرحبا', 'السلام', 'هلا', 'اهلا', 'مساء', 'صباح'])) {
            return {
                text: this.getRandomResponse([
                    'مرحباً بك! أنا المساعد الذكي لمحمد الأشرافي 👋 كيف يمكنني مساعدتك اليوم؟',
                    'أهلاً وسهلاً! سعيد بوجودك هنا 😊 ما الذي تود الاستفسار عنه؟',
                    'مرحباً! أنا هنا لمساعدتك في كل ما يخص خدمات محمد الأشرافي 🎨'
                ])
            };
        }
        
        // Services inquiry
        if (this.matchesKeywords(lowerMessage, ['خدمات', 'خدمة', 'اعمال', 'تصميم', 'موقع', 'تطبيق', 'شعار'])) {
            return this.generateServicesResponse(lowerMessage);
        }
        
        // Pricing inquiry
        if (this.matchesKeywords(lowerMessage, ['سعر', 'كم', 'تكلفة', 'فلوس', 'مبلغ', 'ثمن'])) {
            return this.generatePricingResponse(lowerMessage);
        }
        
        // Contact inquiry
        if (this.matchesKeywords(lowerMessage, ['تواصل', 'اتصال', 'واتساب', 'ايميل', 'هاتف', 'رقم'])) {
            return {
                text: `يمكنك التواصل مع محمد الأشرافي عبر:

📱 واتساب: ${this.knowledgeBase.contact.whatsapp}
📧 إيميل: ${this.knowledgeBase.contact.email}
🕐 ساعات العمل: ${this.knowledgeBase.contact.workingHours}

هل تريد أن أرسل له رسالة بخصوص استفسارك؟`,
                isHTML: false,
                actions: ['show_contact_options']
            };
        }
        
        // Time inquiry
        if (this.matchesKeywords(lowerMessage, ['وقت', 'مدة', 'متى', 'كم يوم', 'مده'])) {
            return {
                text: `⏰ مدة تنفيذ المشاريع تختلف حسب النوع:

• التصاميم البسيطة: 2-3 أيام
• الهوية البصرية الكاملة: 5-7 أيام  
• المواقع الإلكترونية: 2-4 أسابيع
• التطبيقات: 2-3 أسابيع

ما نوع المشروع الذي تفكر فيه؟`
            };
        }
        
        // Portfolio inquiry
        if (this.matchesKeywords(lowerMessage, ['اعمال', 'مشاريع', 'بورتفوليو', 'نماذج', 'امثلة'])) {
            return {
                text: `🎨 يمكنك مشاهدة أعمال محمد الأشرافي في قسم "معرض أعمالي" أعلى الصفحة.

لديه أكثر من ${this.knowledgeBase.about.projects} مشروع منجز في:
• الهوية البصرية والشعارات
• المواقع الإلكترونية
• تطبيقات الهاتف
• التصميم الإعلاني

هل تود مشاهدة أعمال في مجال معين؟`,
                actions: ['scroll_to_portfolio']
            };
        }
        
        // About inquiry
        if (this.matchesKeywords(lowerMessage, ['من', 'عن', 'خبرة', 'تعريف', 'نبذة'])) {
            return {
                text: `👨‍🎨 محمد الأشرافي مصمم محترف متخصص في:

✅ ${this.knowledgeBase.about.experience} خبرة في التصميم
✅ ${this.knowledgeBase.about.projects} مشروع منجز
✅ ${this.knowledgeBase.about.satisfaction} نسبة رضا العملاء
✅ متخصص في: ${this.knowledgeBase.about.specialties.join('، ')}

هدفه تحويل أفكارك إلى تصاميم رائعة وتجارب بصرية لا تُنسى!`
            };
        }
        
        // FAQ responses
        const faqResponse = this.checkFAQ(lowerMessage);
        if (faqResponse) {
            return { text: faqResponse };
        }
        
        // Contact request
        if (this.matchesKeywords(lowerMessage, ['اريد', 'محتاج', 'طلب', 'عايز', 'مشروع'])) {
            return {
                text: `رائع! يسعد محمد الأشرافي خدمتك 🎉

لتقديم أفضل خدمة، يرجى إخباري:
1️⃣ ما نوع المشروع المطلوب؟
2️⃣ ما هو الهدف من المشروع؟
3️⃣ هل لديك ميزانية محددة؟

أم تفضل التواصل المباشر معه؟`,
                actions: ['request_project_details'],
                notifyAdmin: true
            };
        }
        
        // Thank you
        if (this.matchesKeywords(lowerMessage, ['شكرا', 'تسلم', 'جزاك', 'مشكور'])) {
            return {
                text: this.getRandomResponse([
                    'عفواً، سعيد بمساعدتك! 😊 هل تحتاج أي شيء آخر؟',
                    'لا شكر على واجب! محمد الأشرافي في خدمتك دائماً 🎨',
                    'أهلاً وسهلاً! لا تتردد في السؤال عن أي شيء 👍'
                ])
            };
        }
        
        // Default response with suggestions
        return {
            text: `شكراً لرسالتك! 😊 

أستطيع مساعدتك في:
🎨 معلومات عن الخدمات والأسعار
⏰ مدة تنفيذ المشاريع
📞 معلومات التواصل
👨‍🎨 نبذة عن محمد الأشرافي
💼 مشاهدة الأعمال السابقة

أو يمكنك التواصل مباشرة مع محمد عبر الواتساب للحصول على استشارة مخصصة!`,
            actions: ['suggest_direct_contact']
        };
    }
    
    generateServicesResponse(message) {
        let specificService = null;
        
        // Check for specific service
        Object.keys(this.knowledgeBase.services).forEach(key => {
            const service = this.knowledgeBase.services[key];
            if (message.includes(service.name.toLowerCase()) || 
                message.includes(key) ||
                (key === 'web' && (message.includes('موقع') || message.includes('ويب'))) ||
                (key === 'app' && (message.includes('تطبيق') || message.includes('ابلكيشن'))) ||
                (key === 'branding' && (message.includes('شعار') || message.includes('هوية'))) ||
                (key === 'print' && (message.includes('اعلان') || message.includes('طباعة')))) {
                specificService = service;
            }
        });
        
        if (specificService) {
            return {
                text: `🎯 ${specificService.name}

💰 السعر: ${specificService.price}
⏰ المدة: ${specificService.duration}

📋 يشمل:
${specificService.includes.map(item => `• ${item}`).join('\n')}

📝 ${specificService.description}

هل تود طلب عرض سعر مخصص لمشروعك؟`,
                actions: ['offer_quote']
            };
        }
        
        return {
            text: `🎨 خدمات محمد الأشرافي المتخصصة:

${Object.values(this.knowledgeBase.services).map(service => 
                `• ${service.name} - ${service.price}`
            ).join('\n')}

أي خدمة تهمك أكثر؟ يمكنني تقديم تفاصيل أكثر!`,
            actions: ['show_service_details']
        };
    }
    
    generatePricingResponse(message) {
        if (message.includes('موقع') || message.includes('ويب')) {
            return { text: `💰 أسعار تصميم المواقع الإلكترونية:

🌟 موقع تعريفي بسيط: 1500-2500 ريال
🌟 موقع شركة متكامل: 3000-5000 ريال  
🌟 متجر إلكتروني: 4000-8000 ريال
🌟 موقع مخصص متقدم: حسب المتطلبات

الأسعار تشمل التصميم والتطوير والاستضافة لسنة!` };
        }
        
        return {
            text: `💰 نظرة عامة على الأسعار:

${Object.values(this.knowledgeBase.services).map(service => 
                `• ${service.name}: ${service.price}`
            ).join('\n')}

💡 الأسعار تختلف حسب:
- تعقيد المشروع
- المتطلبات الخاصة
- المدة الزمنية المطلوبة

تود عرض سعر مخصص لمشروعك؟`
        };
    }
    
    checkFAQ(message) {
        for (const faq of this.knowledgeBase.faqs) {
            if (message.includes(faq.question.toLowerCase().split(' ')[0])) {
                return `❓ ${faq.question}\n\n✅ ${faq.answer}`;
            }
        }
        return null;
    }
    
    matchesKeywords(message, keywords) {
        return keywords.some(keyword => message.includes(keyword));
    }
    
    getRandomResponse(responses) {
        return responses[Math.floor(Math.random() * responses.length)];
    }
    
    executeAction(action) {
        switch (action) {
            case 'show_contact_options':
                this.showContactOptions();
                break;
            case 'scroll_to_portfolio':
                document.getElementById('portfolio')?.scrollIntoView({ behavior: 'smooth' });
                break;
            case 'request_project_details':
                this.requestProjectDetails();
                break;
            case 'offer_quote':
                this.offerQuote();
                break;
            case 'suggest_direct_contact':
                this.suggestDirectContact();
                break;
        }
    }
    
    showContactOptions() {
        const contactHTML = `
            <div class="contact-options">
                <h4>طرق التواصل:</h4>
                <div class="contact-buttons">
                    <a href="https://wa.me/${this.knowledgeBase.contact.whatsapp}" class="contact-btn whatsapp" target="_blank">
                        <i class="fab fa-whatsapp"></i> واتساب
                    </a>
                    <a href="mailto:${this.knowledgeBase.contact.email}" class="contact-btn email" target="_blank">
                        <i class="fas fa-envelope"></i> إيميل
                    </a>
                </div>
            </div>
        `;
        
        this.addMessage(contactHTML, 'bot', { isHTML: true });
    }
    
    requestProjectDetails() {
        this.currentContext = 'project_details';
        this.awaitingInput = true;
        
        this.addMessage('أخبرني عن مشروعك بالتفصيل وسأساعدك في الحصول على أفضل خدمة! 📝', 'bot');
    }
    
    offerQuote() {
        const quoteMessage = `للحصول على عرض سعر مخصص ومفصل:

📋 يرجى إرسال:
• وصف مفصل للمشروع
• أمثلة أو مراجع إن وجدت
• الميزانية المتوقعة
• التاريخ المطلوب للتسليم

📱 التواصل المباشر للحصول على الرد الأسرع!`;
        
        this.addMessage(quoteMessage, 'bot');
        setTimeout(() => this.suggestDirectContact(), 2000);
    }
    
    suggestDirectContact() {
        const suggestion = `🚀 للحصول على خدمة أسرع وأكثر تخصصاً، انقر هنا للتواصل المباشر:

<div class="direct-contact">
    <a href="https://wa.me/${this.knowledgeBase.contact.whatsapp}?text=مرحباً محمد، أتيت من الموقع وأريد الاستفسار عن خدماتك" class="contact-btn whatsapp-direct" target="_blank">
        💚 تواصل عبر الواتساب
    </a>
</div>`;
        
        this.addMessage(suggestion, 'bot', { isHTML: true });
    }
    
    notifyAdmin(userMessage, botResponse) {
        const notification = `🤖 محادثة جديدة مع المساعد الذكي:

👤 العميل: "${userMessage}"
🤖 الرد: "${botResponse}"

الوقت: ${new Date().toLocaleString('ar-EG')}
الصفحة: ${window.location.href}`;
        
        // Send to WhatsApp after delay
        setTimeout(() => {
            const whatsappUrl = `https://wa.me/${this.knowledgeBase.contact.whatsapp}?text=${encodeURIComponent(notification)}`;
            
            // Create notification element
            this.showNotificationToast('تم إرسال استفسارك لمحمد الأشرافي 📤');
            
            // Auto-send notification (can be made optional)
            if (this.userPreferences.autoNotifyAdmin !== false) {
                window.open(whatsappUrl, '_blank');
            }
        }, 3000);
    }
    
    showTypingIndicator() {
        const messagesContainer = document.getElementById('ai-messages');
        const typingDiv = document.createElement('div');
        typingDiv.className = 'ai-message bot typing-indicator';
        typingDiv.innerHTML = `
            <div class="message-content">
                <div class="typing-dots">
                    <span></span>
                    <span></span>
                    <span></span>
                </div>
                جاري الكتابة...
            </div>
        `;
        
        messagesContainer.appendChild(typingDiv);
        messagesContainer.scrollTop = messagesContainer.scrollHeight;
    }
    
    hideTypingIndicator() {
        const typingIndicator = document.querySelector('.typing-indicator');
        if (typingIndicator) {
            typingIndicator.remove();
        }
    }
    
    handleTyping() {
        // Show that user is typing (for future enhancements)
        if (this.currentContext && this.awaitingInput) {
            clearTimeout(this.typingTimeout);
            this.typingTimeout = setTimeout(() => {
                // Handle context-specific responses
            }, 1000);
        }
    }
    
    initializeGreeting() {
        setTimeout(() => {
            if (this.conversationHistory.length === 0) {
                const greetings = [
                    'مرحباً! أنا المساعد الذكي لمحمد الأشرافي 👋 هل تحتاج مساعدة في أي شيء؟',
                    'أهلاً بك! سعيد بزيارتك لموقع محمد الأشرافي 🎨 كيف يمكنني خدمتك؟',
                    'مرحباً! لديك أسئلة عن خدمات التصميم؟ أنا هنا للمساعدة! 😊'
                ];
                
                this.addMessage(this.getRandomResponse(greetings), 'bot', { showTime: true });
            }
        }, 2000);
    }
    
    loadUserPreferences() {
        try {
            return JSON.parse(localStorage.getItem('ai_preferences') || '{}');
        } catch {
            return {};
        }
    }
    
    saveUserPreferences() {
        localStorage.setItem('ai_preferences', JSON.stringify(this.userPreferences));
    }
    
    loadConversationHistory() {
        try {
            const history = JSON.parse(localStorage.getItem('ai_conversation') || '[]');
            this.conversationHistory = history;
            
            // Restore recent messages (last 5)
            const recentMessages = history.slice(-5);
            recentMessages.forEach(msg => {
                this.addMessage(msg.content, msg.sender, { 
                    isHTML: msg.isHTML,
                    showTime: false 
                });
            });
        } catch {
            this.conversationHistory = [];
        }
    }
    
    saveConversationHistory() {
        try {
            // Keep only last 50 messages
            const limitedHistory = this.conversationHistory.slice(-50);
            localStorage.setItem('ai_conversation', JSON.stringify(limitedHistory));
        } catch (e) {
            console.warn('Could not save conversation history:', e);
        }
    }
    
    trackEvent(eventName, data = {}) {
        // Track AI interactions for analytics
        if (typeof gtag !== 'undefined') {
            gtag('event', eventName, {
                'custom_parameter': JSON.stringify(data),
                'page_title': document.title,
                'page_location': window.location.href
            });
        }
        
        // Also save to local analytics
        const events = JSON.parse(localStorage.getItem('ai_analytics') || '[]');
        events.push({
            event: eventName,
            data,
            timestamp: new Date().toISOString(),
            page: window.location.pathname
        });
        
        // Keep only last 100 events
        localStorage.setItem('ai_analytics', JSON.stringify(events.slice(-100)));
    }
    
    showNotificationToast(message, duration = 3000) {
        const toast = document.createElement('div');
        toast.className = 'ai-notification-toast';
        toast.textContent = message;
        toast.style.cssText = `
            position: fixed;
            top: 100px;
            right: 20px;
            background: #10b981;
            color: white;
            padding: 1rem 1.5rem;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            z-index: 10000;
            transform: translateX(100%);
            transition: transform 0.3s ease;
        `;
        
        document.body.appendChild(toast);
        
        setTimeout(() => {
            toast.style.transform = 'translateX(0)';
        }, 100);
        
        setTimeout(() => {
            toast.style.transform = 'translateX(100%)';
            setTimeout(() => document.body.removeChild(toast), 300);
        }, duration);
    }
    
    handlePageHidden() {
        // Save state when user leaves page
        this.saveConversationHistory();
        this.saveUserPreferences();
    }
    
    // Public methods for external interaction
    addExternalMessage(message, sender = 'system') {
        this.addMessage(message, sender, { showTime: true });
    }
    
    clearHistory() {
        this.conversationHistory = [];
        localStorage.removeItem('ai_conversation');
        document.getElementById('ai-messages').innerHTML = `
            <div class="ai-message bot">
                <div class="message-content">
                    تم مسح المحادثة. مرحباً من جديد! كيف يمكنني مساعدتك؟
                </div>
            </div>
        `;
    }
    
    setUserPreference(key, value) {
        this.userPreferences[key] = value;
        this.saveUserPreferences();
    }
}

// Add CSS for typing indicator
const typingCSS = `
.typing-indicator .typing-dots {
    display: inline-flex;
    gap: 4px;
    margin-left: 8px;
}

.typing-indicator .typing-dots span {
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background: var(--primary-color);
    animation: typing 1.4s infinite ease-in-out;
}

.typing-indicator .typing-dots span:nth-child(1) {
    animation-delay: -0.32s;
}

.typing-indicator .typing-dots span:nth-child(2) {
    animation-delay: -0.16s;
}

@keyframes typing {
    0%, 80%, 100% {
        transform: scale(0.8);
        opacity: 0.5;
    }
    40% {
        transform: scale(1);
        opacity: 1;
    }
}

.contact-options {
    text-align: center;
    padding: 1rem;
    background: var(--light-color);
    border-radius: 8px;
    margin: 0.5rem 0;
}

.contact-buttons {
    display: flex;
    gap: 0.5rem;
    justify-content: center;
    margin-top: 1rem;
}

.contact-btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    border-radius: 6px;
    text-decoration: none;
    color: white;
    font-size: 0.9rem;
    transition: all 0.3s ease;
}

.contact-btn.whatsapp {
    background: #25D366;
}

.contact-btn.email {
    background: #EA4335;
}

.contact-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.direct-contact {
    text-align: center;
    margin: 1rem 0;
}

.whatsapp-direct {
    background: linear-gradient(135deg, #25D366, #128C7E);
    color: white;
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    text-decoration: none;
    display: inline-block;
    font-weight: 600;
    transition: all 0.3s ease;
}

.whatsapp-direct:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(37, 211, 102, 0.3);
}
`;

// Add the CSS to the document
const styleSheet = document.createElement('style');
styleSheet.textContent = typingCSS;
document.head.appendChild(styleSheet);

// Initialize AI Assistant when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    window.aiAssistant = new AIAssistant();
});

// Export for external use
window.AIAssistant = AIAssistant;
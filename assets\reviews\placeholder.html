<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Client Avatar Generator</title>
    <style>
        .avatar {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 32px;
            font-weight: bold;
            color: white;
            font-family: 'Cairo', Arial, sans-serif;
            margin: 10px;
        }
        .client1 { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
        .client2 { background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); }
        .client3 { background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); }
    </style>
</head>
<body>
    <div class="avatar client1">أ</div>
    <div class="avatar client2">ف</div>
    <div class="avatar client3">خ</div>
    
    <script>
        // This file is just for reference - the actual avatars are handled by CSS placeholders
        console.log('Client avatar placeholders ready');
    </script>
</body>
</html>

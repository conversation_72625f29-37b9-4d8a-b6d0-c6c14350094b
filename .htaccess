# .htaccess file for <PERSON>'s Website
# Apache server configuration

# Enable mod_rewrite
RewriteEngine On

# Security Headers
<IfModule mod_headers.c>
    # X-Frame-Options
    Header always set X-Frame-Options DENY
    
    # X-Content-Type-Options
    Header always set X-Content-Type-Options nosniff
    
    # X-XSS-Protection
    Header always set X-XSS-Protection "1; mode=block"
    
    # Referrer Policy
    Header always set Referrer-Policy "strict-origin-when-cross-origin"
    
    # Content Security Policy
    Header always set Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' https://cdnjs.cloudflare.com https://fonts.googleapis.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com https://cdnjs.cloudflare.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self' https:; media-src 'self';"
    
    # Permissions Policy
    Header always set Permissions-Policy "geolocation=(), microphone=(), camera=(), fullscreen=(self)"
    
    # Remove Server signature
    Header unset Server
    Header unset X-Powered-By
</IfModule>

# HTTPS Redirect
RewriteCond %{HTTPS} off
RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]

# WWW Redirect (choose one)
# Force WWW
# RewriteCond %{HTTP_HOST} !^www\. [NC]
# RewriteRule ^(.*)$ https://www.%{HTTP_HOST}%{REQUEST_URI} [L,R=301]

# Force non-WWW
RewriteCond %{HTTP_HOST} ^www\.(.+)$ [NC]
RewriteRule ^(.*)$ https://%1%{REQUEST_URI} [L,R=301]

# Clean URLs - Remove .html extension
RewriteCond %{REQUEST_FILENAME} !-d
RewriteCond %{REQUEST_FILENAME} !-f
RewriteRule ^([^\.]+)$ $1.html [NC,L]

# Redirect old URLs to new ones (if needed)
# RewriteRule ^old-page/?$ /new-page [R=301,L]

# Custom Error Pages
ErrorDocument 404 /404.html
ErrorDocument 403 /403.html
ErrorDocument 500 /500.html

# File Compression
<IfModule mod_deflate.c>
    # Compress HTML, CSS, JavaScript, Text, XML and fonts
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/json
    AddOutputFilterByType DEFLATE application/ld+json
    AddOutputFilterByType DEFLATE application/manifest+json
    AddOutputFilterByType DEFLATE application/rdf+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/schema+json
    AddOutputFilterByType DEFLATE application/vnd.geo+json
    AddOutputFilterByType DEFLATE application/vnd.ms-fontobject
    AddOutputFilterByType DEFLATE application/x-font-ttf
    AddOutputFilterByType DEFLATE application/x-javascript
    AddOutputFilterByType DEFLATE application/x-web-app-manifest+json
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE font/eot
    AddOutputFilterByType DEFLATE font/opentype
    AddOutputFilterByType DEFLATE image/bmp
    AddOutputFilterByType DEFLATE image/svg+xml
    AddOutputFilterByType DEFLATE image/vnd.microsoft.icon
    AddOutputFilterByType DEFLATE image/x-icon
    AddOutputFilterByType DEFLATE text/cache-manifest
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/javascript
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/vcard
    AddOutputFilterByType DEFLATE text/vnd.rim.location.xloc
    AddOutputFilterByType DEFLATE text/vtt
    AddOutputFilterByType DEFLATE text/x-component
    AddOutputFilterByType DEFLATE text/x-cross-domain-policy
    AddOutputFilterByType DEFLATE text/xml
</IfModule>

# Browser Caching
<IfModule mod_expires.c>
    ExpiresActive on
    
    # Images
    ExpiresByType image/jpg "access plus 1 month"
    ExpiresByType image/jpeg "access plus 1 month"
    ExpiresByType image/gif "access plus 1 month"
    ExpiresByType image/png "access plus 1 month"
    ExpiresByType image/svg+xml "access plus 1 month"
    ExpiresByType image/webp "access plus 1 month"
    ExpiresByType image/ico "access plus 1 month"
    ExpiresByType image/x-icon "access plus 1 month"
    
    # Videos
    ExpiresByType video/mp4 "access plus 1 month"
    ExpiresByType video/webm "access plus 1 month"
    
    # Fonts
    ExpiresByType font/ttf "access plus 1 year"
    ExpiresByType font/otf "access plus 1 year"
    ExpiresByType font/woff "access plus 1 year"
    ExpiresByType font/woff2 "access plus 1 year"
    ExpiresByType application/font-woff "access plus 1 year"
    
    # CSS and JavaScript
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
    ExpiresByType text/javascript "access plus 1 month"
    
    # HTML
    ExpiresByType text/html "access plus 1 day"
    
    # Data
    ExpiresByType application/json "access plus 1 day"
    ExpiresByType application/xml "access plus 1 day"
    ExpiresByType text/xml "access plus 1 day"
    
    # Manifest files
    ExpiresByType application/manifest+json "access plus 1 week"
    ExpiresByType text/cache-manifest "access plus 0 seconds"
    
    # Default
    ExpiresDefault "access plus 1 month"
</IfModule>

# Cache Control Headers
<IfModule mod_headers.c>
    # CSS and JS files
    <FilesMatch "\.(css|js)$">
        Header set Cache-Control "public, max-age=2592000"
    </FilesMatch>
    
    # Image files
    <FilesMatch "\.(jpg|jpeg|png|gif|svg|webp|ico)$">
        Header set Cache-Control "public, max-age=2592000"
    </FilesMatch>
    
    # Font files
    <FilesMatch "\.(ttf|otf|woff|woff2|eot)$">
        Header set Cache-Control "public, max-age=31536000"
    </FilesMatch>
    
    # HTML files
    <FilesMatch "\.html$">
        Header set Cache-Control "public, max-age=86400"
    </FilesMatch>
</IfModule>

# File Access Protection
<Files ".htaccess">
    Order allow,deny
    Deny from all
</Files>

<Files "*.env">
    Order allow,deny
    Deny from all
</Files>

<Files "package*.json">
    Order allow,deny
    Deny from all
</Files>

<Files "tsconfig.json">
    Order allow,deny
    Deny from all
</Files>

# Directory Browsing
Options -Indexes

# Follow Symbolic Links
Options +FollowSymLinks

# Default Charset
AddDefaultCharset UTF-8

# MIME Types
<IfModule mod_mime.c>
    # Web fonts
    AddType application/font-woff2 .woff2
    AddType application/font-woff .woff
    AddType application/vnd.ms-fontobject .eot
    AddType font/ttf .ttf
    AddType font/otf .otf
    
    # Manifest
    AddType application/manifest+json .webmanifest
    AddType application/x-web-app-manifest+json .webapp
    
    # Media files
    AddType video/mp4 .mp4
    AddType video/webm .webm
    AddType audio/mp3 .mp3
    AddType audio/ogg .ogg
    
    # Other
    AddType image/svg+xml .svg
    AddType image/webp .webp
    AddType text/cache-manifest .appcache
</IfModule>

# PHP Settings (if using PHP)
<IfModule mod_php7.c>
    php_value upload_max_filesize 10M
    php_value post_max_size 10M
    php_value max_execution_time 300
    php_value max_input_vars 1000
</IfModule>

# Prevent access to sensitive files
<FilesMatch "(^#.*#|\.(bak|config|dist|fla|inc|ini|log|psd|sh|sql|sw[op])|~)$">
    Order allow,deny
    Deny from all
    Satisfy All
</FilesMatch>

# Custom redirects for portfolio items (if needed)
# RewriteRule ^portfolio/([a-zA-Z0-9-]+)/?$ /#portfolio?item=$1 [R=301,L]

# API endpoints (for future backend integration)
# RewriteRule ^api/contact/?$ /api/contact.php [QSA,L]
# RewriteRule ^api/portfolio/?$ /api/portfolio.php [QSA,L]
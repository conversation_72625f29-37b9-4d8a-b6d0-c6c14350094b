[build]
  # Build command (not needed for static site, but good to have)
  command = "echo 'Static site - no build needed'"
  
  # Publish directory
  publish = "."
  
  # Functions directory (if needed later)
  functions = "netlify/functions"

[build.environment]
  # Node version
  NODE_VERSION = "18"

# Headers for security and performance
[[headers]]
  for = "/*"
  [headers.values]
    # Security headers
    X-Frame-Options = "DENY"
    X-XSS-Protection = "1; mode=block"
    X-Content-Type-Options = "nosniff"
    Referrer-Policy = "strict-origin-when-cross-origin"
    Permissions-Policy = "camera=(), microphone=(), geolocation=()"
    
    # Performance headers
    Cache-Control = "public, max-age=31536000"

# Specific headers for HTML files
[[headers]]
  for = "/*.html"
  [headers.values]
    Cache-Control = "public, max-age=0, must-revalidate"

# Headers for CSS and JS files
[[headers]]
  for = "/styles/*.css"
  [headers.values]
    Cache-Control = "public, max-age=31536000, immutable"

[[headers]]
  for = "/js/*.js"
  [headers.values]
    Cache-Control = "public, max-age=31536000, immutable"

# Headers for images and assets
[[headers]]
  for = "/assets/*"
  [headers.values]
    Cache-Control = "public, max-age=31536000, immutable"

# PWA headers for manifest
[[headers]]
  for = "/manifest.json"
  [headers.values]
    Content-Type = "application/manifest+json"
    Cache-Control = "public, max-age=0, must-revalidate"

# Service Worker headers
[[headers]]
  for = "/service-worker.js"
  [headers.values]
    Cache-Control = "public, max-age=0, must-revalidate"
    Service-Worker-Allowed = "/"

# Redirects for SPA behavior (if needed)
[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200
  conditions = {Role = ["admin"]}

# Force HTTPS
[[redirects]]
  from = "http://mohamed-alashrafi.netlify.app/*"
  to = "https://mohamed-alashrafi.netlify.app/:splat"
  status = 301
  force = true

# Arabic language support
[[headers]]
  for = "/*"
  [headers.values]
    Content-Language = "ar"
    
# Font optimization
[[headers]]
  for = "*.woff2"
  [headers.values]
    Cache-Control = "public, max-age=31536000, immutable"
    
[[headers]]
  for = "*.woff"
  [headers.values]
    Cache-Control = "public, max-age=31536000, immutable"
